import {Routes} from '@angular/router';
import {BlankComponent} from './layouts/blank/blank.component';
import {LayoutContainerComponent} from "./layouts/layout-container/pages/layout-container.component";
import { HomeExport } from './pages/HomeExport/HomeExport.component';
import { ChoHaiSan } from './pages/ChoHaiSan/ChoHaiSan.component';
import { ChonMon } from './pages/ChonMon/ChonMon.component';
import { LoginComponent } from './pages/auth/login/login.component';
import { RegisterComponent } from './pages/auth/register/register.component';
import { ForgotPasswordComponent } from './pages/auth/forgot-password/forgot-password.component';
import { TransactionHistoryComponent } from './pages/transaction-history/transaction-history.component';
import { TaiKhoanComponent } from './pages/tai-khoan/tai-khoan.component';
import {MenuComponent} from "./pages/menu/menu.component";
import {PayComponent} from "./pages/pay/pay.component";

export const ROUTES_NAME = {
  DETAIL: '/home/<USER>',
}
export const routes: Routes = [
  {
    path: '',
    component: LayoutContainerComponent,
    children: [
      {
        path: '',
        redirectTo: '/home',
        pathMatch: 'full',
      },
      // {
      //   path: '',
      //   redirectTo: '/dashboards/dashboard1',
      //   pathMatch: 'full',
      // },
      {
        path: 'cho-hai-san',
        component: ChoHaiSan,
      },
      {
        path: 'chon-mon',
        component: ChonMon,
      },
      {
        path: 'dang-nhap',
        component: LoginComponent,
      },
      {
        path: 'dang-ky',
        component: RegisterComponent,
      },
      {
        path: 'quen-mat-khau',
        component: ForgotPasswordComponent,
      },
      {
        path: 'lich-su-giao-dich',
        component: TransactionHistoryComponent,
      },
      {
        path: 'tai-khoan',
        component: TaiKhoanComponent,
      },
      {
        path: 'menu',
        component: MenuComponent,
      },
      {
        path: 'pay',
        component: PayComponent,
      },
      {
        path: 'tin-tuc',
        loadChildren: () => import('./pages/tin-tuc/tin-tuc.routes').then(m => m.TinTucRoutes),
      },
      {
        path: 'chi-tiet',
        loadChildren: () => import('./pages/chi-tiet/chi-tiet.routes').then(m => m.ChiTietRoutes),
      },
      {
        path: 'home',
        loadChildren: () => import('./pages/home/<USER>').then(m => m.HomeRoutes),
      },
      {
        path: 'post-news',
        loadChildren: () =>
          import('./pages/post-news/post-news.routes').then(m => m.PostNewsRoutes),
      },
      {
        path: 'list-news',
        loadChildren: () =>
          import('./pages/list-news/list-news.routes').then(m => m.ListNewsRoutes),
      },
      {
        path: 'starter',
        loadChildren: () => import('./pages/pages.routes').then(m => m.PagesRoutes),
      },
      {
        path: 'dashboards',
        loadChildren: () =>
          import('./pages/dashboards/dashboards.routes').then(m => m.DashboardsRoutes),
      },
      {
        path: 'ui-components',
        loadChildren: () =>
          import('./pages/ui-components/ui-components.routes').then(m => m.UiComponentsRoutes),
      },
      {
        path: 'forms',
        loadChildren: () => import('./pages/forms/forms.routes').then(m => m.FormsRoutes),
      },
      {
        path: 'charts',
        loadChildren: () => import('./pages/charts/charts.routes').then(m => m.ChartsRoutes),
      },
      {
        path: 'apps',
        loadChildren: () => import('./pages/apps/apps.routes').then(m => m.AppsRoutes),
      },
      {
        path: 'widgets',
        loadChildren: () => import('./pages/widgets/widgets.routes').then(m => m.WidgetsRoutes),
      },
      {
        path: 'tables',
        loadChildren: () => import('./pages/tables/tables.routes').then(m => m.TablesRoutes),
      },
      {
        path: 'datatable',
        loadChildren: () =>
          import('./pages/datatable/datatable.routes').then(m => m.DatatablesRoutes),
      },
      {
        path: 'theme-pages',
        loadChildren: () =>
          import('./pages/theme-pages/theme-pages.routes').then(m => m.ThemePagesRoutes),
      },
    ],
  },
  {
    path: '',
    component: BlankComponent,
    children: [
      {
        path: 'authentication',
        loadChildren: () =>
          import('./pages/authentication/authentication.routes').then(m => m.AuthenticationRoutes),
      },
      {
        path: 'landingpage',
        loadChildren: () =>
          import('./pages/theme-pages/landingpage/landingpage.routes').then(
            m => m.LandingPageRoutes,
          ),
      },
    ],
  },
  {
    path: '**',
    redirectTo: 'authentication/error',
  },
];
