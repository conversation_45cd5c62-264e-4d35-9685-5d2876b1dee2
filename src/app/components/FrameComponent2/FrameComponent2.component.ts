import {Component, ViewEncapsulation, HostBinding, Input, Output, EventEmitter} from '@angular/core';

import { CommonModule } from '@angular/common';
@Component({
  selector: 'frame-component2',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
  templateUrl: './FrameComponent2.component.html',
  styleUrls: ['./FrameComponent2.component.scss'],
})
export class FrameComponent2 {
  @HostBinding('style.display') display = 'contents';

  constructor() {}

  /** Value props */
  @Input() image: string = '';
  @Input() name: string = '';
  @Input() price: string = '';
  @Input() priceDiscount: string = '';
  @Input() discount: string = '';
  /** Style props */
  @Input() frameDivBackgroundImage: string | number = '';

  @Output() onBuyClick = new EventEmitter<void>();

  get frameDivStyle() {
    return {
      'background-image': this.frameDivBackgroundImage,
    };
  }

  onBuyNowClick() {
    this.onBuyClick.emit();
  }
}
