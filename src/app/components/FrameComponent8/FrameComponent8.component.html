<div class="frame-parent7">
  <div class="frame-parent8">
    <div class="frame-parent9">
      <div class="frame-wrapper7">
        <div class="t-mn-parent">
          <div class="t-mn">
            <section class="frame-parent13">
              <div class="t-mn-wrapper">
                <h3 class="t-mn1">ĐẶT MÓN</h3>
              </div>
              <div class="frame-parent14">
                <div class="frame-parent15">
                  <div class="frame-wrapper8">
                    <div class="">
                      <div class="product-image-container position-relative">
                        <img
                          class="product-image rounded product-main-image"
                          [src]="productImage || 'assets/images/placeholder.svg'"
                          alt="Product Image"
                          (error)="handleImageError($event)" />

                        <!-- Rating overlay -->
                        <div class="rating-overlay position-absolute">
                          <img
                            class="rating-stars"
                            loading="lazy"
                            [src]="ratingImage"
                            alt="Rating" />
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="frame-parent16">
                    <div class="frame-parent17">
                      <div class="tn-mn-n-wrapper">
                        <h3 class="tn-mn-n">Tên món ăn</h3>
                      </div>
                      <div class="frame-parent18">
                        <div class="frame-parent19">
                          <div class="frame-wrapper9">
                            <div class="frame-parent20">
                              <div class="vn-parent">
                                <h3 class="vn">80,000 VNĐ</h3>
                                <div class="vn-wrapper">
                                  <div class="vn1">90,000 VNĐ</div>
                                </div>
                              </div>
                              <div class="frame-parent21">
                                <div class="view-more-button">
                                  <img
                                    class="frame-child13"
                                    loading="lazy"
                                    alt=""
                                    src="assets/group-2241.svg"
                                  />
                                </div>
                                <div class="nh-gi">512 đánh giá</div>
                              </div>
                            </div>
                          </div>
                          <div class="tiu-option">Tiêu đề option</div>
                        </div>
                        <div class="frame-wrapper11">
                          <div class="rectangle-parent11">
                            <div class="frame-child14"></div>
                            <div class="option-value">-10%</div>
                          </div>
                        </div>
                      </div>
                      <div class="frame-parent22">
                        <button *ngFor="let option of options" class="group-button" [class.active]="optionSelected === option" (click)="selectOption(option)">
                          <div class="frame-child15"></div>
                          <div class="op2">{{option}}</div>
                        </button>
                      </div>
                    </div>
                    <div class="frame-wrapper12">
                      <div class="s-lng-parent">
                        <div class="s-lng">Số lượng</div>
                        <div class="quantity-controls">
                          <button class="control-container">
                            <div class="control-container-child"></div>
                            <i class="quantity-buttons">-</i>
                          </button>
                          <input class="quantity-input" type="number" value="1" />
                          <button class="control-container">
                            <div class="control-container-child"></div>
                            <i class="quantity-buttons">+</i>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="product-description">
                      <div class="m-t-sn-phm-parent">
                        <div class="m-t-sn">MÔ TẢ SẢN PHẨM</div>
                        <div class="description-content">
                          <div *ngIf="!expandedContent; else elseExpanded" class="chng-ta-vn">
                            {{description.substring(0, 100)}}
                          </div>
                          <ng-template  #elseExpanded class="chng-ta-vn">
                            {{description}}
                          </ng-template>
                        </div>
                      </div>
                    </div>
                    <div class="frame-wrapper13">
                      <div class="view-more-button-parent">
                        <div class="view-more-button" (click)="viewMore()">
                          <div class="xem-thm">{{expandedContent ? 'Thu gọn' : 'Xem thêm'}}</div>
                        </div>
                        <img class="arrow2-5-icon" [style.rotate]="expandedContent ? '180deg' : '0deg'" alt="" src="assets/arrow2-5.svg" />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="frame-wrapper14">
                  <div class="vector-parent">
                    <img class="vector-icon" alt="" src="assets/vector-9.svg" />
                    <div class="frame-child19"></div>
                    <div class="add-to-cart-actions">
                      <div class="cart-actions-container">
                        <button class="cart-actions">
                          <div class="cart-actions-child"></div>
                          <div class="thm-vo-gi">Thêm vào giỏ</div>
                        </button>
                        <button class="rectangle-parent15">
                          <div class="frame-child20"></div>
                          <div class="mua-ngay3">Mua ngay</div>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
