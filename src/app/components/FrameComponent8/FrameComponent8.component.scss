.quantity-input {
  width: 40px;
  height: 100%;
  text-align: center;
}
.image-615-wrapper,
.trang-ch-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-11) 0 0;
}
.trang-ch-wrapper {
  padding: 7px var(--padding-4) 0 0;
}
.menu1 {
  position: relative;
  cursor: pointer;
  z-index: 1;
}
.gii-thiu-parent {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--gap-20);
}
.frame-wrapper4 {
  width: 173px;
  box-sizing: border-box;
}
.frame-wrapper4,
.siu-th-container,
.tin-tc-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 7px var(--padding-15) 0 0;
}
.tin-tc-container {
  padding: 7px var(--padding-20) 0 0;
}
.image-579-icon1 {
  width: 20px;
  position: relative;
  max-height: 100%;
  object-fit: cover;
  z-index: 2;
}
.rectangle-input {
  border: 0;
  outline: 0;
  max-width: 100%;
  background-color: var(--color-steelblue-100);
  height: 32px;
  width: 161px;
  position: absolute;
  margin: 0 !important;
  bottom: -8px;
  left: -44px;
  border-radius: var(--br-5);
  z-index: 1;
}
.rectangle-parent9 {
  flex-direction: row;
  position: relative;
}
.frame-wrapper5,
.image-579-parent,
.rectangle-parent9 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.image-579-parent {
  flex-direction: row;
  gap: var(--gap-10);
}
.frame-wrapper5 {
  flex-direction: column;
  padding: 7px 0 0;
}
.bell-notification-1-container {
  flex-direction: column;
  padding: var(--padding-1) 0 0;
}
.bell-notification-1-container,
.frame-parent11,
.frame-wrapper6 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.frame-parent11 {
  flex-direction: row;
  gap: var(--gap-14);
}
.frame-wrapper6 {
  flex-direction: column;
  padding: 7px var(--padding-3) 0 0;
}
.image-541-icon1 {
  width: 20px;
  height: 20px;
  position: relative;
  object-fit: cover;
  z-index: 2;
}
.image-541-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-6) 0 0;
}
.frame-child12 {
  height: 40px;
  width: 81px;
  position: absolute;
  margin: 0 !important;
  top: calc(50% - 20px);
  left: -43px;
  border-radius: var(--br-100);
  border: 1px solid var(--color-steelblue-100);
  box-sizing: border-box;
  z-index: 1;
}
.image-533-icon1 {
  height: 32px;
  width: 32px;
  position: relative;
  object-fit: cover;
  z-index: 2;
}
.frame-parent10,
.frame-parent12 {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  gap: var(--gap-8);
}
.frame-parent10 {
  width: 870px;
  justify-content: space-between;
  gap: var(--gap-20);
  max-width: 100%;
}
.t-mn1 {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 700;
  font-family: inherit;
  display: inline-block;
  min-width: 96px;
}
.t-mn-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.image-icon13 {
  height: 287px;
  width: 302px;
  position: relative;
  border-radius: var(--br-10);
  object-fit: cover;
  display: none;
}
.group-icon {
  height: 19px;
  width: 131.6px;
  position: relative;
  z-index: 1;
}
.frame-wrapper8,
.image-parent1 {
  display: flex;
  justify-content: flex-start;
}
.image-parent1 {
  align-self: stretch;
  flex: 1;
  border-radius: var(--br-10);
  flex-direction: row;
  align-items: flex-end;
  padding: var(--padding-11) 13px;
  background-image: url(../../../assets/<EMAIL>);
  background-size: cover;
  background-repeat: no-repeat;
  background-position: top;
}
.frame-wrapper8 {
  flex-direction: column;
  align-items: flex-start;
  padding: var(--padding-2) 0 0;
  box-sizing: border-box;
}
.rating-overlay {
  bottom: 12px;
  left: 12px;
}
.tn-mn-n {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 600;
  font-family: inherit;
}
.tn-mn-n-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-3);
  font-family: var(--font-body);
}
.vn,
.vn1 {
  position: relative;
}
.vn {
  margin: 0;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
}
.vn1 {
  text-decoration: line-through;
  display: inline-block;
  min-width: 84px;
}
.vn-parent,
.vn-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.vn-wrapper {
  flex-direction: column;
  padding: var(--padding-4) 0 0;
  font-size: var(--font-size-14);
  color: var(--color-darkgray-100);
}
.vn-parent {
  align-self: stretch;
  flex-direction: row;
  gap: var(--gap-6);
}
.frame-child13 {
  align-self: stretch;
  height: 11px;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  flex-shrink: 0;
}
.nh-gi {
  position: relative;
  display: inline-block;
  min-width: 88px;
}
.frame-parent21 {
  flex-direction: row;
  gap: 12.4px;
  font-size: var(--font-size-14);
  color: var(--color-darkslategray-100);
}
.frame-parent20,
.frame-parent21,
.frame-wrapper9 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.frame-parent20 {
  flex: 1;
  flex-direction: column;
  gap: var(--gap-11);
}
.frame-wrapper9 {
  align-self: stretch;
  flex-direction: row;
  padding: 0 0 0 var(--padding-3);
}
.tiu-option {
  position: relative;
  font-size: var(--font-size-16);
  color: var(--color-darkgray-200);
}
.frame-parent19 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 21px;
  min-width: 135px;
}
.frame-child14 {
  height: 15px;
  width: 41px;
  position: relative;
  border-radius: var(--br-3);
  background-color: var(--color-crimson-100);
  display: none;
}
.option-value {
  position: relative;
  z-index: 1;
}
.rectangle-parent11 {
  align-self: stretch;
  border-radius: var(--br-3);
  background-color: var(--color-crimson-100);
  flex-direction: row;
  padding: var(--padding-1) var(--padding-3) 0;
  z-index: 1;
}
.frame-parent18,
.frame-wrapper11,
.rectangle-parent11 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.frame-wrapper11 {
  width: 41px;
  flex-direction: column;
  padding: var(--padding-4) 0 0;
  box-sizing: border-box;
  font-size: var(--font-size-12);
  color: var(--color-white);
}
.frame-parent18 {
  align-self: stretch;
  flex-direction: row;
  row-gap: var(--gap-20);
  color: var(--color-crimson-200);
}
.frame-child15 {
  height: 38px;
  width: 68px;
  position: relative;
  border-radius: var(--br-4);
  background-color: var(--color-whitesmoke);
  display: none;
}
.op2 {
  flex: 1;
  position: relative;
  font-size: var(--font-size-16);
  font-family: var(--font-body);
  color: var(--color-darkslategray-100);
  text-align: center;
  z-index: 1;
}
.group-button {
  cursor: pointer;
  border: 0;
  padding: 9px 0 var(--padding-10);
  background-color: var(--color-whitesmoke);
  flex: 1;
  border-radius: var(--br-4);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  &.active {
    border: 1px solid var(--color-steelblue-300);
    background-color: var(--color-white);
  }
}
.cart-actions:hover,
.group-button:hover {
  background-color: var(--color-gainsboro-200);
}
.options-container,
.options-container-child {
  height: 38px;
  border-radius: var(--br-4);
  background-color: var(--color-white);
  border: 1px solid var(--color-steelblue-300);
  box-sizing: border-box;
}
.options-container-child {
  width: 68px;
  position: relative;
  display: none;
}
.options-container {
  flex: 1;
  flex-direction: row;
  padding: 7px 0 var(--padding-8);
}
.frame-parent17,
.frame-parent22,
.options-container {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.frame-parent22 {
  width: 220px;
  flex-direction: row;
  gap: var(--gap-8);
  text-align: center;
  font-size: var(--font-size-16);
  color: var(--color-darkslategray-100);
}
.frame-parent17 {
  flex-direction: column;
  gap: 7px;
  font-size: var(--font-size-20);
  color: var(--color-black);
}
.s-lng {
  position: relative;
}
.control-container-child {
  height: 38px;
  width: 38px;
  position: relative;
  border-radius: var(--br-4);
  background-color: var(--color-whitesmoke);
  display: none;
}
.quantity-buttons {
  flex: 1;
  position: relative;
  z-index: 1;
}
.control-container {
  flex: 1;
  border: 0;
  border-radius: var(--br-4);
  background-color: var(--color-whitesmoke);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 9px 0 var(--padding-10);
}
.frame-child17 {
  height: 38px;
  width: 38px;
  position: relative;
  border-radius: var(--br-4);
  border: 1px solid var(--color-whitesmoke);
  box-sizing: border-box;
  display: none;
}
.quantity-controls,
.rectangle-parent13 {
  align-self: stretch;
  flex: 1;
  flex-direction: row;
}
.rectangle-parent13 {
  border-radius: var(--br-4);
  border: 1px solid var(--color-whitesmoke);
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 7px 0 var(--padding-8);
}
.quantity-controls {
  gap: var(--gap-8);
  text-align: center;
  font-size: var(--font-size-16);
  color: var(--color-darkslategray-100);
}
.frame-wrapper12,
.quantity-controls,
.s-lng-parent {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.s-lng-parent {
  align-self: stretch;
  flex: 1;
  flex-direction: column;
  gap: var(--gap-8);
}
.frame-wrapper12 {
  width: 132px;
  flex-direction: row;
  padding: 0 var(--padding-1) 17px;
  box-sizing: border-box;
}
.chng-ta-vn,
.m-t-sn {
  position: relative;
  display: inline-block;
}
.m-t-sn {
  min-width: 121px;
}
.chng-ta-vn {
  flex: 1;
  line-height: 19px;
  max-width: 100%;
}
.description-content {
  align-self: stretch;
  flex-direction: row;
  padding: 0 0 0 var(--padding-1);
  box-sizing: border-box;
  color: var(--color-darkslategray-200);
}
.description-content,
.m-t-sn-phm-parent,
.product-description {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  max-width: 100%;
}
.m-t-sn-phm-parent {
  flex: 1;
  flex-direction: column;
  gap: 17px;
  margin-right: 40px;
}
.product-description {
  align-self: stretch;
  flex-direction: row;
  padding: 0 0 0 var(--padding-3);
  box-sizing: border-box;
  color: var(--color-gray-200);
}
.xem-thm {
  position: relative;
  flex-shrink: 0;
}
.view-more-button {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-3) 0 0;
}
.arrow2-5-icon {
  height: 24px;
  width: 24px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  z-index: 1;
}
.view-more-button-parent {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.frame-parent15,
.frame-parent16,
.frame-wrapper13 {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}
.frame-wrapper13 {
  width: calc(100% - 40px);
  flex-direction: row;
  justify-content: center;
  /* padding: 0 var(--padding-20); */
  box-sizing: border-box;
  text-align: center;
  margin-right: 40px;
}
.frame-parent15,
.frame-parent16 {
  justify-content: flex-start;
}
.frame-parent16 {
  flex: 1;
  flex-direction: column;
  gap: 17px;
  min-width: 272px;
}
.frame-parent15 {
  align-self: stretch;
  flex-direction: row;
  gap: 41px;
}
.frame-child19,
.vector-icon {
  align-self: stretch;
  position: relative;
}
.product-image {
  max-width: 100%;
  width: 300px;
}
.vector-icon {
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.frame-child19 {
  height: 71px;
  background-color: var(--color-white);
  display: none;
}
.cart-actions-child {
  height: 48px;
  width: 168px;
  position: relative;
  border-radius: var(--br-4);
  background-color: var(--color-whitesmoke);
  display: none;
}
.thm-vo-gi {
  flex: 1;
  position: relative;
  font-size: var(--font-size-16);
  font-family: var(--font-body);
  color: var(--color-darkslategray-100);
  text-align: center;
  z-index: 2;
}
.cart-actions {
  cursor: pointer;
  border: 0;
  padding: var(--padding-15) 0 var(--padding-14);
  background-color: var(--color-whitesmoke);
  flex: 1;
  border-radius: var(--br-4);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  min-width: 109px;
  z-index: 1;
}
.frame-child20 {
  height: 48px;
  width: 167px;
  position: relative;
  border-radius: var(--br-4);
  background-color: var(--color-steelblue-300);
  display: none;
}
.mua-ngay3 {
  flex: 1;
  position: relative;
  font-size: var(--font-size-16);
  font-family: var(--font-body);
  color: var(--color-white);
  text-align: center;
  z-index: 1;
}
.cart-actions-container,
.rectangle-parent15 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.rectangle-parent15 {
  cursor: pointer;
  border: 0;
  padding: var(--padding-15) 0 var(--padding-14);
  background-color: var(--color-steelblue-300);
  border-radius: var(--br-4);
  box-sizing: border-box;
  min-width: 109px;
  z-index: 1;
}
.rectangle-parent15:hover {
  background-color: var(--color-steelblue-200);
}
.cart-actions-container {
  gap: var(--gap-8);
  max-width: 100%;
}
.add-to-cart-actions,
.vector-parent {
  justify-content: flex-start;
  box-sizing: border-box;
}
.add-to-cart-actions {
  align-self: stretch;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  padding: 0 var(--padding-16);
  max-width: 100%;
}
.vector-parent {
  width: 375px;
  flex-direction: column;
  padding: 0 0 var(--padding-12);
  gap: var(--gap-11);
}
.frame-parent13,
.frame-parent14,
.frame-wrapper14,
.vector-parent {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}
.frame-wrapper14 {
  width: 719px;
  flex-direction: row;
  justify-content: center;
  padding: 0 var(--padding-20);
  box-sizing: border-box;
}
.frame-parent13,
.frame-parent14 {
  flex-direction: column;
  justify-content: flex-start;
}
.frame-parent14 {
  align-self: stretch;
  gap: 61px;
  font-size: var(--font-size-14);
  color: var(--color-darkgray-200);
  font-family: var(--font-body);
}
.frame-parent13 {
  gap: 49px;
  flex-shrink: 0;
  text-align: left;
  font-size: var(--font-size-20);
  color: var(--color-gray-200);
  font-family: var(--font-body);
  width: 100%;
}
.cart-indicator {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 50%;
  background-color: var(--color-crimson-200);
  width: 100%;
  height: 100%;
  z-index: 1;
}
.shopping-cart-12 {
  height: 24px;
  width: 24px;
  position: absolute;
  margin: 0 !important;
  bottom: -16px;
  left: -11px;
  overflow: hidden;
  flex-shrink: 0;
}
.item-count {
  position: relative;
  z-index: 2;
}
.shopping-cart-1-parent {
  position: absolute;
  top: 1px;
  left: 3px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.cart-indicator-parent {
  margin-top: -54px;
  height: 16px;
  width: 16px;
  position: relative;
  flex-shrink: 0;
}
.t-mn {
  align-self: stretch;
  background-color: var(--color-white);
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  gap: 384px;
  max-width: 100%;
  z-index: 4;
}
.frame-child21 {
  height: 14px;
  width: 55px;
  position: relative;
  border-radius: var(--br-3);
  background: linear-gradient(135deg, #00e05a, #00d7e4);
  display: none;
}
.shipping-icon {
  height: 10px;
  width: 10px;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  z-index: 1;
}
.free-ship1 {
  position: relative;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
  z-index: 1;
}
.rectangle-parent16 {
  border-radius: var(--br-3);
  background: linear-gradient(135deg, #00e05a, #00d7e4);
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-2) var(--padding-3) 0;
  gap: var(--gap-2);
  z-index: 1;
}
.frame-child22 {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: var(--br-3);
  background: linear-gradient(135deg, var(--color-crimson-100), #ac3137);
  width: 100%;
  height: 100%;
  z-index: 1;
}
.free-ship-icon {
  position: absolute;
  top: 2px;
  left: 3px;
  width: 10px;
  height: 10px;
  overflow: hidden;
  z-index: 2;
}
.rectangle-parent17 {
  height: 14px;
  width: 38px;
  position: relative;
}
.sale1 {
  position: relative;
  letter-spacing: -0.5px;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
  z-index: 2;
}
.frame-parent24,
.sale-discount {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.sale-discount {
  flex-direction: column;
  padding: var(--padding-2) 0 0;
  margin-left: -23px;
  position: relative;
}
.frame-parent24 {
  flex-direction: row;
  padding: 0 var(--padding-5) 0 0;
}
.frame-parent23 {
  flex-direction: row;
  gap: var(--gap-4);
}
.frame-parent23,
.frame-wrapper15,
.t-mn-parent {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.frame-wrapper15 {
  flex-direction: row;
  padding: 0 var(--padding-8);
  text-align: left;
  font-size: var(--font-size-8);
  font-family: var(--font-fugaz-one);
}
.t-mn-parent {
  width: 961px;
  flex-direction: column;
  gap: 25px;
  max-width: 100%;
}
.frame-wrapper7 {
  align-self: stretch;
  flex-direction: row;
  align-items: flex-start;
  text-align: center;
  font-size: var(--font-size-12);
  font-family: var(--font-body);
}
.frame-parent8,
.frame-parent9,
.frame-wrapper7,
.wine-collection {
  display: flex;
  justify-content: flex-start;
  max-width: 100%;
}
.frame-parent9 {
  flex: 1;
  flex-direction: column;
  align-items: flex-end;
  gap: 255px;
  min-width: 679px;
}
.frame-parent8,
.wine-collection {
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: wrap;
  align-content: flex-start;
}
.frame-parent8 {
  width: 1188px;
  gap: 44px;
}
.wine-collection {
  flex: 1;
  gap: 98px;
}
.frame-parent7,
.product-display {
  display: flex;
  max-width: 100%;
  text-align: left;
}
.product-display {
  align-self: stretch;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0 23px 0 0;
  box-sizing: border-box;
  font-size: var(--font-size-16);
  color: var(--color-steelblue-100);
  font-family: var(--font-body);
}
.frame-parent7 {
  width: 1229px;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  gap: var(--gap-30);
  font-size: var(--font-size-14);
  color: var(--color-white);
  font-family: var(--font-varela);
}
@media screen and (max-width: 1100px) {
  .frame-parent10 {
    flex-wrap: wrap;
  }
  .frame-wrapper8 {
    flex: 1;
  }
  .frame-parent15 {
    flex-wrap: wrap;
  }
  .t-mn {
    gap: 192px;
    box-sizing: border-box;
  }
  .frame-parent9 {
    gap: 127px;
    min-width: 100%;
  }
}
@media screen and (max-width: 750px) {
  .t-mn1 {
    font-size: var(--font-size-16);
  }
  .tn-mn-n,
  .vn {
    font-size: var(--font-size-16);
  }
  .frame-parent18 {
    flex-wrap: wrap;
  }
  .frame-parent15 {
    gap: var(--gap-20);
  }
  .frame-parent14 {
    gap: var(--gap-15);
  }
  .frame-parent13 {
    gap: var(--gap-24);
  }
  .frame-parent9 {
    gap: var(--gap-32);
  }
  .wine-collection {
    gap: var(--gap-24);
  }
}
@media screen and (max-width: 450px) {

  .cart-actions-container {
    flex-wrap: wrap;
  }
  .frame-parent14 {
    gap: var(--gap-30);
  }
  .t-mn {
    gap: 96px;
    box-sizing: border-box;
  }
  .frame-parent9 {
    gap: var(--gap-64);
  }
  .frame-parent8 {
    gap: var(--gap-22);
  }
  .wine-collection {
    gap: 49px;
  }
}
