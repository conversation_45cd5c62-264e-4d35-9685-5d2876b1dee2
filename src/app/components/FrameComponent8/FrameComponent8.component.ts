import { Component, ViewEncapsulation, HostBinding, Input } from '@angular/core';

import { FrameComponent6 } from '../FrameComponent6/FrameComponent6.component';
import {NgForOf, NgIf} from "@angular/common";
@Component({
  selector: 'frame-component8',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [FrameComponent6, NgForOf, NgIf],
  templateUrl: './FrameComponent8.component.html',
  styleUrls: ['./FrameComponent8.component.scss'],
})
export class FrameComponent8 {
  @HostBinding('style.display') display = 'contents';
  options = ['Op1', 'Op2', 'Op3'];
  optionSelected = 'Op1';
  expandedContent = false;
  description = 'Chúng ta vẫn biết rằng, làm việc với một đoạn văn bản dễ đọc và rõ nghĩadễ gây rối trí và cản trở việc tập trung vào yếu tố trình bày văn bản.Chúng ta vẫn biết rằng, làm việc với một đoạn văn bản dễ đọc và rõ nghĩadễ gây rối trí và cản trở việc tập trung vào yếu tố trình bày văn bản.Chúng ta vẫn biết rằng, làm việc với một đoạn văn bản dễ đọc và rõ nghĩadễ gây rối trí và cản trở việc tập trung vào yếu tố trình bày văn bản.';

  // Input properties để truyền ảnh từ component cha
  @Input() productImage: string = 'assets/images/products/s1.jpg'; // Default image
  @Input() ratingImage: string = 'assets/group-2217.svg'; // Rating stars image

  constructor() {}

  onMENUTextClick() {
    // Please sync "Menu" to the project
  }

  onTINTCTextClick() {
    // Please sync "Tin tuc" to the project
  }

  selectOption(option: string) {
    this.optionSelected = option;
  }

  viewMore() {
    this.expandedContent = !this.expandedContent;
  }

  /**
   * Xử lý lỗi hình ảnh
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    imgElement.src = 'assets/images/placeholder.svg';
    imgElement.classList.add('img-error');
  }
}
