import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
  selector: 'app-loading-dialog',
  standalone: true,
  imports: [
    MatProgressSpinnerModule
  ],
  template: `
    <div class="loading-dialog-container">
      <mat-spinner diameter="30" color="primary"></mat-spinner>
    </div>
  `,
  styles: [`
    .loading-dialog-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 15px;
    }
  `]
})
export class LoadingDialogComponent {
  constructor(public dialogRef: MatDialogRef<LoadingDialogComponent>) {
    // Make dialog non-closable by ESC key or clicking outside
    this.dialogRef.disableClose = true;
  }
}
