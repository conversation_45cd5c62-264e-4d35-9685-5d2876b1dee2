import { Component, ViewEncapsulation, HostBinding, OnInit } from '@angular/core';
import {RouterModule, ActivatedRoute, Router} from '@angular/router';
import { CommonModule } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { DropdownMenuComponent } from '../../dropdown-menu/dropdown-menu.component';
import { BookingDialogComponent } from '../../shared/components/booking-dialog/booking-dialog.component';
import { IconsModule } from '../../icons.module';

@Component({
  selector: 'app-navbar',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  imports: [DropdownMenuComponent, RouterModule, CommonModule, IconsModule],
  templateUrl: './navbar.component.html',
  styleUrls: ['./navbar.component.scss'],
})
export class NavbarComponent implements OnInit {
  @HostBinding('style.display') display = 'contents';
  isMobileMenuOpen = false;

  constructor(private dialog: MatDialog, private route: ActivatedRoute, public router: Router,) {}

  ngOnInit() {
    // Check if URL has booking parameter
    this.route.queryParams.subscribe(params => {
      if (params['booking'] !== undefined) {
        // Auto-trigger booking dialog
        this.onBookingClick();
      }
    });
  }

  onMENUTextClick() {
    this.router.navigate(['/menu']);
  }

  onBookingClick() {
    const dialogRef = this.dialog.open(BookingDialogComponent, {
      width: '30%',
      maxWidth: '700px',
      disableClose: false,
      autoFocus: true,
      panelClass: 'booking-dialog-container',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Booking created:', result);
        // Có thể thêm logic xử lý sau khi đặt bàn thành công
      }
    });
  }

  onPayment() {
    this.router.navigate(['/pay']);
  }

  // Mobile menu functionality
  toggleMobileMenu() {
    this.isMobileMenuOpen = !this.isMobileMenuOpen;
  }

  closeMobileMenu() {
    this.isMobileMenuOpen = false;
  }
}
