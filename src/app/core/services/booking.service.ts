import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import { BranchStore } from './menu.service';

// Interface cho request đặt bàn theo API thực tế
export interface BookingRequest {
  orderId: string;
  userId: string;
  storeId: string;
  storeManagerId: string;
  customerName: string;
  phone: string;
  note?: string;
  branchId: string;
  branchName: string;
  branchAddress: string;
  price: number;
  branchPhone: string;
  reservationDate: string;
  reservationTime: string;
  numberOfGuests: number;
  specialRequirements?: string;
  occasion?: string;
  isPayOnline: number;
  paymentMethod: number;
  typeBooking: number;
  status: number;
  items: BookingItem[];
}

export interface BookingItem {
  serviceName: string;
  price: number;
  numberOfGuests: number;
  reservationDate: string;
  reservationTime: string;
}

export interface BookingResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Interface cho form input từ user
export interface BookingFormData {
  branchId: string;
  customerName: string;
  phoneNumber: string;
  email: string;
  reservationDate: Date;
  adultCount: number;
  childCount: number;
  mealType: string;
  notes?: string;
}

export interface BranchOption {
  value: string;
  label: string;
  address?: string;
  phone?: string;
}

export interface MealOption {
  value: string;
  label: string;
  description?: string;
}

@Injectable({
  providedIn: 'root',
})
export class BookingService {
  private apiUrl = environment.apiUrl;

  // Thông tin cố định từ API
  private readonly defaultStoreId = '63205907b33a347a94aa987e';
  private readonly defaultStoreManagerId = '6320565fb33a347a94aa987c';
  private readonly defaultUserId = '623df63a51282810737ff2b4';

  constructor(private http: HttpClient) {}

  /**
   * Tạo đặt bàn mới theo API thực tế
   * @param formData Dữ liệu từ form
   * @param selectedBranch Thông tin chi nhánh đã chọn
   */
  createBooking(
    formData: BookingFormData,
    selectedBranch: BranchStore,
  ): Observable<BookingResponse> {
    const orderId = this.generateOrderId();
    const reservationDateTime = this.formatDateTime(formData.reservationDate);
    const totalGuests = formData.adultCount + formData.childCount;

    const bookingRequest: BookingRequest = {
      orderId: orderId,
      userId: this.defaultUserId,
      storeId: this.defaultStoreId,
      storeManagerId: this.defaultStoreManagerId,
      customerName: formData.customerName,
      phone: formData.phoneNumber,
      note: formData.notes || '',
      branchId: formData.branchId,
      branchName: selectedBranch.name,
      branchAddress: selectedBranch.address,
      price: 0,
      branchPhone: selectedBranch.phone || selectedBranch.hotline || '',
      reservationDate: reservationDateTime.date,
      reservationTime: reservationDateTime.time,
      numberOfGuests: totalGuests,
      specialRequirements: formData.notes || '',
      occasion: this.getMealTypeDescription(formData.mealType),
      isPayOnline: 0,
      paymentMethod: 0,
      typeBooking: 5, // Đặt bàn nhà hàng
      status: 0,
      items: [
        {
          serviceName: 'Đặt bàn nhà hàng',
          price: 0,
          numberOfGuests: totalGuests,
          reservationDate: reservationDateTime.date,
          reservationTime: reservationDateTime.time,
        },
      ],
    };

    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization:
        'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2ODRhYTg1ZGY5Y2IwNzUxNjg2ZWQwYTgiLCJ0eXBlIjoyLCJjb2RlIjoxODk3LCJiaXJ0aGRheSI6MTc0OTcyMzIyOTg5NSwibW9kaWZ5QXQiOjE3NDk3ODg4MDgwMDAsImNyZWF0ZUF0IjoxNzQ5NzIzMjI5MDAwLCJwb2ludCI6MCwiY29uZmlybVBob25lIjoxLCJjb25maXJtRW1haWwiOjAsIndhbGxldCI6MCwiZnVuZHMiOjAsImZlZSI6MCwic2VydmljZXNHYXMiOjEsInNlcnZpY2VzU2hvd3Jvb20iOjEsInNlcnZpY2VzUGFya2luZyI6MSwic2VydmljZXNTcGEiOjEsInNlcnZpY2VzSG90ZWwiOjEsInNlcnZpY2VzRXhhbWluYXRpb24iOjEsInNlcnZpY2VzU3RvcmUiOjEsIm5vdGlmaWNhdGlvbnNCaWxsUmV0dXJuIjowLCJjb3VudE1lc3NhZ2UiOjAsImNvdW50T25saW5lU2Vzc2lvbiI6MCwibm90aWZpY2F0aW9ucyI6MCwic3RhdHVzU3RvcmUiOjEsInN0YXR1cyI6MSwicGhvbmUiOiIwOTg3NjU0MzIxIiwiYWRkcmVzc0xpc3QiOltdLCJhZGRyZXNzIjoiIiwibG9jYXRpb24iOiIiLCJzdHJlZXQiOiIiLCJlbWFpbCI6IiIsInVzZXJOYW1lIjoiIiwiZnVsbE5hbWUiOiJOZ3V5ZW4gVmFuIEEiLCJfX3YiOjAsInBpY3R1cmUiOiIvdGVtcGxhdGUvZGVmYXVsdC1hdmF0YXIuanBnIiwiaWF0IjoxNzQ5ODA2NTYzLCJleHAiOjI1MzgyMDY1NjN9.L54gCV3CqHmzPMQ-Fx6wGVOU6xoMQR899kyDrK1DJ88',
      'access-token':
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2ODRhYTg1ZGY5Y2IwNzUxNjg2ZWQwYTgiLCJ0eXBlIjoyLCJjb2RlIjoxODk3LCJiaXJ0aGRheSI6MTc0OTcyMzIyOTg5NSwibW9kaWZ5QXQiOjE3NDk3ODg4MDgwMDAsImNyZWF0ZUF0IjoxNzQ5NzIzMjI5MDAwLCJwb2ludCI6MCwiY29uZmlybVBob25lIjoxLCJjb25maXJtRW1haWwiOjAsIndhbGxldCI6MCwiZnVuZHMiOjAsImZlZSI6MCwic2VydmljZXNHYXMiOjEsInNlcnZpY2VzU2hvd3Jvb20iOjEsInNlcnZpY2VzUGFya2luZyI6MSwic2VydmljZXNTcGEiOjEsInNlcnZpY2VzSG90ZWwiOjEsInNlcnZpY2VzRXhhbWluYXRpb24iOjEsInNlcnZpY2VzU3RvcmUiOjEsIm5vdGlmaWNhdGlvbnNCaWxsUmV0dXJuIjowLCJjb3VudE1lc3NhZ2UiOjAsImNvdW50T25saW5lU2Vzc2lvbiI6MCwibm90aWZpY2F0aW9ucyI6MCwic3RhdHVzU3RvcmUiOjEsInN0YXR1cyI6MSwicGhvbmUiOiIwOTg3NjU0MzIxIiwiYWRkcmVzc0xpc3QiOltdLCJhZGRyZXNzIjoiIiwibG9jYXRpb24iOiIiLCJzdHJlZXQiOiIiLCJlbWFpbCI6IiIsInVzZXJOYW1lIjoiIiwiZnVsbE5hbWUiOiJOZ3V5ZW4gVmFuIEEiLCJfX3YiOjAsInBpY3R1cmUiOiIvdGVtcGxhdGUvZGVmYXVsdC1hdmF0YXIuanBnIiwiaWF0IjoxNzQ5ODA2NTYzLCJleHAiOjI1MzgyMDY1NjN9.L54gCV3CqHmzPMQ-Fx6wGVOU6xoMQR899kyDrK1DJ88',
    });

    return this.http.post<BookingResponse>(
      `${this.apiUrl}/user/api/booking-show-room?output=json`,
      bookingRequest,
      { headers },
    );
  }

  /**
   * Lấy danh sách chi nhánh từ API getBranchStores
   * @param storeId ID của store
   */
  getBranches(storeId: string = this.defaultStoreId): Observable<BranchOption[]> {
    return this.http
      .get<any>(`${this.apiUrl}/user/api/get-products-by-storeid.html/${storeId}`)
      .pipe(
        map(response => {
          if (response && response.data && response.data.branchStore) {
            return response.data.branchStore.map((branch: BranchStore) => ({
              value: branch._id,
              label: branch.name,
              address: branch.address,
              phone: branch.phone || branch.hotline,
            }));
          }
          return [];
        }),
      );
  }

  /**
   * Lấy danh sách loại suất ăn (mock data)
   */
  getMealTypes(): Observable<MealOption[]> {
    const mealTypes: MealOption[] = [
      { value: 'breakfast', label: 'Bữa sáng', description: 'Phục vụ từ 6:00 - 10:00' },
      { value: 'lunch', label: 'Bữa trưa', description: 'Phục vụ từ 11:00 - 14:00' },
      { value: 'dinner', label: 'Bữa tối', description: 'Phục vụ từ 17:00 - 22:00' },
      { value: 'buffet', label: 'Buffet', description: 'Buffet tự chọn' },
    ];
    return new Observable(observer => {
      observer.next(mealTypes);
      observer.complete();
    });
  }

  /**
   * Tạo order ID ngẫu nhiên
   */
  private generateOrderId(): string {
    return Date.now().toString() + Math.random().toString(36).substring(2, 7);
  }

  /**
   * Format datetime thành date và time riêng biệt
   */
  private formatDateTime(date: Date): { date: string; time: string } {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return {
      date: `${year}-${month}-${day}`,
      time: `${hours}:${minutes}`,
    };
  }

  /**
   * Lấy mô tả loại suất ăn
   */
  private getMealTypeDescription(mealType: string): string {
    const mealTypes: { [key: string]: string } = {
      breakfast: 'Bữa sáng',
      lunch: 'Bữa trưa',
      dinner: 'Bữa tối',
      buffet: 'Buffet',
    };
    return mealTypes[mealType] || 'Đặt bàn';
  }
}
