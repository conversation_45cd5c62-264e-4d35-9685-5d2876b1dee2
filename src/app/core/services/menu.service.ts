import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from './api.service';
import { ApiResponse } from '../models/api-response.model';

export interface CategoryMenu {
  _id: string;
  name: string;
  picture: string;
  code: number;
  modifyAt: number;
  createAt: number;
  type: string;
  order: number;
  status: number;
  __v: number;
}
export interface Classification {
  _id: string;
  mass: string;
  priceOld: string;
  price: string;
  name: string;
}

export interface Classify {
  _id: string;
  name: string;
  data: Classification[];
}

export interface Product {
  _id: string;
  userId: string;
  name: string;
  storeId: string;
  categoryId: string;
  description: string;
  trademark: string;
  price: number;
  transport: string;
  thumbail: string;
  code: number;
  createAt: number;
  classify: Classify[];
  typeProduct: number;
  revenue: number;
  watched: number;
  status: number;
  size: any[];
  pictures: any[];
  priceOld: number | null;
  typeShip: number;
  codeText: string;
  storeName: string;
  categoryName: string;
}

export interface BranchStore {
  _id: string;
  userId: string;
  storeId: string;
  name: string;
  address: string;
  nameUTF: string;
  addressUTF: string;
  content: string;
  lat: string;
  lng: string;
  phone: string;
  timeOpen: string;
  timeClose: string;
  code: number;
  modifyAt: number;
  createAt: number;
  location: {
    type: string;
    coordinates: number[];
  };
  status: number;
  pictures: any[];
  __v: number;
  hotline: string;
  district: string;
  province: string;
  ward: string;
}

export interface StoreProductsResponse {
  products: Product[];
  totalProducts: number;
  branchStore: BranchStore[];
}
@Injectable({
  providedIn: 'root'
})
export class MenuService {

  constructor(private apiService: ApiService) { }

  /**
   * Lấy danh sách danh mục
   */
  getMenuCategories(): Observable<ApiResponse<any>> {
    return this.apiService.get<any>(`user/api/categories-by-type?cat=0`);
  }

  /**
   * Lấy danh sách sp theo danh muc
   * @param pet lo i pet
   * @returns Observable<ApiResponse<any>>
   */
  getMenuWithCategory(pet: string): Observable<ApiResponse<any>> {
    return this.apiService.get<any>(`user/api/get-products-for-pet`, {
      pet
    });
  }

  /**
   * Lấy danh sách cơ sở theo storeId
   * @param storeId ID của cửa hàng
   * @returns Observable<ApiResponse<StoreProductsResponse>>
   */
  getBranchStores(storeId: string): Observable<ApiResponse<StoreProductsResponse>> {
    return this.apiService.get<StoreProductsResponse>(`user/api/get-products-by-storeid.html/${storeId}`);
  }

}
