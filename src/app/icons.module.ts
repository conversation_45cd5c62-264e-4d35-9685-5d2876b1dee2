import { NgModule } from '@angular/core';
import { TablerIconsModule } from 'angular-tabler-icons';
import {
  IconMapPin,
  IconPhone,
  IconHome,
  IconStar,
  IconStarFilled,
  IconMinus,
  IconPlus,
  IconShoppingCart,
  IconBolt,
  IconChevronDown,
  IconUser,
  IconMenu
} from 'angular-tabler-icons/icons';

const icons = {
  IconMapPin,
  IconPhone,
  IconHome,
  IconStar,
  IconStarFilled,
  IconMinus,
  IconPlus,
  IconShoppingCart,
  IconBolt,
  IconChevronDown,
  IconUser,
  IconMenu
};

@NgModule({
  imports: [TablerIconsModule.pick(icons)],
  exports: [TablerIconsModule],
})
export class IconsModule {}
