.chon-mon-child {
  width: 1440px;
  height: 309px;
  position: absolute;
  margin: 0 !important;
  bottom: 68px;
  left: -3px;
}
.frame-main,
.frame-section,
.lobster-showcase {
  align-self: stretch;
  display: flex;
  justify-content: flex-start;
}
.lobster-showcase {
  flex-direction: row;
  align-items: flex-end;
  gap: var(--gap-24);
}
.frame-main,
.frame-section {
  flex-direction: column;
  align-items: flex-start;
  gap: 75.2px;
  max-width: 100%;
}
.frame-main {
  gap: 90.7px;
}
.ch-hi-sn1,
.chon-mon-item {
  position: relative;
  display: none;
}
.ch-hi-sn1 {
  font-size: var(--font-size-24);
  max-width: 100%;
  z-index: 2;
}
.chon-mon-item {
  width: 250px;
  height: 50px;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-100);
  z-index: 3;
}
.chon-mon-inner,
.thc-phm-ti1 {
  position: relative;
  display: none;
  z-index: 4;
}
.chon-mon-inner {
  width: 245px;
  height: 50px;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-100);
  z-index: 5;
}
.chon-mon-child1,
.thc-phm-s1 {
  position: relative;
  display: none;
  z-index: 6;
}
.chon-mon-child1 {
  width: 247px;
  height: 50px;
  border-radius: var(--br-5);
  background-color: var(--color-steelblue-100);
  z-index: 7;
}
.thc-phm-ch1 {
  position: relative;
  display: none;
  z-index: 8;
}
.image-icon1,
.image-icon2 {
  position: absolute;
  margin: 0 !important;
  top: 774px;
}
.image-icon1 {
  width: 302px;
  height: 287px;
  left: 406px;
  border-radius: var(--br-10);
  object-fit: cover;
}
.image-icon2 {
  right: 406px;
}
.image-icon2,
.image-icon3,
.image-icon4,
.image-icon5 {
  width: 302px;
  height: 287px;
  border-radius: var(--br-10);
  object-fit: cover;
}
.image-icon3 {
  position: absolute;
  margin: 0 !important;
  top: 774px;
  right: 80px;
}
.image-icon4,
.image-icon5 {
  left: 80px;
}
.image-icon4 {
  position: absolute;
  margin: 0 !important;
  top: 774px;
}
.image-icon5 {
  top: 1287px;
}
.background,
.image-icon5,
.image-icon6 {
  position: absolute;
  margin: 0 !important;
}
.image-icon6 {
  width: 302px;
  height: 287px;
  bottom: 829px;
  left: 80px;
  border-radius: var(--br-10);
  object-fit: cover;
}
.background {
  cursor: pointer;
  border: 1px solid var(--color-steelblue-100);
  padding: 0;
  background-color: var(--color-darkslateblue);
  height: 50px;
  width: 239px;
  top: calc(50% - 25px);
  right: -43px;
  border-radius: var(--br-5);
  box-sizing: border-box;
}
.xem-thm-sn1 {
  position: relative;
  z-index: 1;
}
.more-products,
.more-products-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.more-products {
  justify-content: flex-start;
  position: relative;
}
.more-products-wrapper {
  align-self: stretch;
  justify-content: center;
  padding: 0 0 23.3px var(--padding-10);
}
.chon-mon-child2 {
  width: 100%;
  height: 75px;
  position: absolute;
  margin: 0 !important;
  top: 0;
  right: 0;
  left: 0;
  background-color: var(--color-steelblue-300);
}
.image-icon7 {
  height: 450px;
  flex: 1;
  position: relative;
  max-width: 100%;
  overflow: hidden;
  object-fit: cover;
  cursor: pointer;
  z-index: 1;
}
.decorative-image,
.sponsor-ad {
  margin: 0 !important;
  position: absolute;
}
.sponsor-ad {
  height: 5px;
  width: 15px;
  bottom: 15px;
  left: 695px;
  border-radius: var(--br-100);
  background-color: var(--color-gray-500);
  z-index: 2;
}
.decorative-image {
  width: 1440px;
  top: 75px;
  left: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  max-width: 100%;
}
.chon-mon-child3,
.chon-mon-child4 {
  width: 15px;
  height: 5px;
  position: relative;
  border-radius: var(--br-100);
  background-color: var(--color-white);
  display: none;
  z-index: 18;
}
.chon-mon-child4 {
  background-color: var(--color-gray-500);
  z-index: 19;
}
.chon-mon-child5 {
  width: 100%;
  height: 100%;
  position: absolute;
  margin: 0 !important;
  top: -3px;
  right: -4px;
  background-color: var(--color-gray-400);
  z-index: 3;
}
.chon-mon {
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: var(--padding-20) var(--padding-80) 29px;
  box-sizing: border-box;
  gap: 171.7px;
  line-height: normal;
  letter-spacing: normal;
  text-align: left;
  font-size: var(--font-size-14);
  color: var(--color-white);
  overflow-y: auto;
  overflow-x: hidden;
}
@media screen and (max-width: 1200px) {
  .lobster-showcase {
    flex-wrap: wrap;
  }
}
@media screen and (max-width: 750px) {
  .frame-section {
    gap: 19px;
  }
  .frame-main {
    gap: 23px;
  }
  .ch-hi-sn1 {
    font-size: var(--font-size-19);
  }
  .chon-mon {
    gap: 43px;
  }
}
@media screen and (max-width: 450px) {
  .frame-section {
    gap: 38px;
  }
  .frame-main {
    gap: 45px;
  }
  .chon-mon {
    gap: 86px;
    padding-left: var(--padding-40);
    padding-right: var(--padding-40);
    box-sizing: border-box;
  }
}
