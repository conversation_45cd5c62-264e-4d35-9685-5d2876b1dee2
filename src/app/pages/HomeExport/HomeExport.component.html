<!-- Bootstrap Carousel Slider - Full Width -->
<div id="homeCarousel" class="carousel slide full-width-carousel" data-bs-ride="carousel">
  <!-- Loading handled by global loading service -->

  <!-- Error State -->
  <div *ngIf="!isLoading && error" class="alert alert-danger m-3">
    {{ error }}
  </div>

  <!-- Carousel Content when data loaded -->
  <ng-container *ngIf="!isLoading && !error && banners.length > 0">
    <!-- Carousel indicators -->
    <div class="carousel-indicators">
      <button
        *ngFor="let banner of banners; let i = index"
        type="button"
        data-bs-target="#homeCarousel"
        [attr.data-bs-slide-to]="i"
        [class.active]="i === 0"
        [attr.aria-current]="i === 0 ? 'true' : null"
        [attr.aria-label]="'Slide ' + (i + 1)"
      ></button>
    </div>

    <!-- Carousel items -->
    <div class="carousel-inner">
      <div
        *ngFor="let banner of banners; let i = index"
        class="carousel-item"
        [class.active]="i === 0"
        (click)="onImageClick(banner)"
      >
        <img
          [src]="getBannerImageUrl(banner)"
          class="d-block w-100 carousel-image"
          [alt]="banner.name"
          style="cursor: pointer"
        />
        <div class="carousel-caption d-none d-md-block">
          <!-- <h5>{{ banner.name }}</h5> -->
        </div>
      </div>
    </div>

    <!-- No banners state -->
    <div *ngIf="banners.length === 0" class="text-center p-5">
      <p>Không có banner nào.</p>
    </div>

    <!-- Navigation controls -->
    <button
      class="carousel-control-prev"
      type="button"
      data-bs-target="#homeCarousel"
      data-bs-slide="prev"
    >
      <span class="carousel-control-prev-icon" aria-hidden="true"></span>
      <span class="visually-hidden">Previous</span>
    </button>
    <button
      class="carousel-control-next"
      type="button"
      data-bs-target="#homeCarousel"
      data-bs-slide="next"
    >
      <span class="carousel-control-next-icon" aria-hidden="true"></span>
      <span class="visually-hidden">Next</span>
    </button>
  </ng-container>
</div>

<div class="home-export">
  <div class="container py-5">
    <div class="row g-4">
      <div class="col-md-6">
        <div class="seafood-content">
          <img src="assets/<EMAIL>" alt="Làng nuôi biển Vân Đồn" class="seafood-image" />
          <div class="content-overlay"></div>
          <h3 class="seafood-title">LÀNG NUÔI BIỂN VÂN ĐỒN</h3>
        </div>
      </div>
      <div class="col-md-6">
        <div class="seafood-content">
          <img src="assets/<EMAIL>" alt="Chợ hải sản" class="seafood-image" />
          <div class="content-overlay"></div>
          <h3 class="seafood-title">
            <p class="m-0">CHỢ HẢI SẢN</p>
            <p class="m-0">Làng nuôi biển Vân Đồn</p>
          </h3>
        </div>
      </div>
    </div>
  </div>
</div>
