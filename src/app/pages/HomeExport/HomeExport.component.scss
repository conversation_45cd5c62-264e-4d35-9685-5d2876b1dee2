// Bootstrap Carousel styles
#homeCarousel {
  margin-bottom: 0;

  .carousel-inner {
    height: 500px; // Fixed height as requested
  }

  .carousel-item {
    height: 100%;
  }

  .carousel-image {
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

// Content section styles
.home-export {
  width: 100%;
  position: relative;
  background-color: var(--color-darkslateblue);
  color: var(--color-white);
  font-family: var(--font-unbounded);
  text-align: left;
  font-size: var(--font-size-20);

  .seafood-content {
    border-radius: var(--br-10);
    height: 287px;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 27px 31px;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    
    .seafood-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      z-index: 0;
    }
    
    .content-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0);
      transition: all 0.3s ease;
    }
    
    &:hover .content-overlay {
      background-color: rgba(0,0,0,0.5);
    }
  }

  .seafood-title {
    margin: 0;
    font-weight: 400;
    text-shadow: 2px 0 0 #01061f, 0 2px 0 #01061f, -2px 0 0 #01061f, 0 -2px 0 #01061f;
    position: relative;
    z-index: 2;
  }
}
// Bootstrap Carousel styles
#homeCarousel {

  .carousel-inner {
    height: 500px; // Fixed height as requested
  }

  .carousel-item {
    height: 100%;
  }

  .carousel-image {
    height: 100%;
    object-fit: cover;
    object-position: center;
  }
}

.home-export {
    width: 100%;
    position: relative;
    background-color: var(--color-darkslateblue);
}
@media screen and (max-width: 750px) {
  .frame-div {
    padding-top: 135px;
    box-sizing: border-box;
  }
  .copyright-lng-nui-bin-vn-wrapper {
    padding-left: 61px;
    padding-right: 61px;
    box-sizing: border-box;
  }
  .home-export {
    gap: 171px;
  }
}
@media screen and (max-width: 450px) {
  .ch-hi-sn-container,
  .lng-nui-bin {
    font-size: var(--font-size-16);
  }
  .copyright-lng-nui-bin-vn-wrapper {
    padding-left: var(--padding-20);
    padding-right: var(--padding-20);
    box-sizing: border-box;
  }
  .home-export {
    gap: 85px;
  }
}
