import { Component } from '@angular/core';
import {<PERSON><PERSON><PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, CommonModule} from "@angular/common";
import {Menu} from "@angular/cdk/menu";
import {CategoryMenu, MenuService, Product, BranchStore} from "../../core/services/menu.service";
import {IconsModule} from "../../icons.module";
import {CurrencyPipe} from "../../pipe/currency.pipe";
import {BookingDialogComponent} from "../../shared/components/booking-dialog/booking-dialog.component";
import {MatDialog} from "@angular/material/dialog";

@Component({
  selector: 'app-menu',
  standalone: true,
  imports: [
    CommonModule,
    IconsModule,
    CurrencyPipe
  ],
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.scss'
})
export class MenuComponent {
  loading = false;
  loadingBranches = false;
  listCategories: CategoryMenu[] = [];
  listProduct: Product[] = [];
  selectedCategoryIndex = 1;
  branches: BranchStore[] = [];

  // Store ID từ API test
  private readonly storeId = '623a9950be781e0ba814f22a';
  constructor(private dialog: MatDialog,private service: MenuService) {

  }

  ngOnInit(): void {
    this.getCategories();
    this.getBranchStores();
  }
  getCategories() {
    this.loading = true;
    this.service.getMenuCategories()
      .subscribe({
        next: (response) => {
          console.log('pump',response)

          if (!response.error && response.data) {
            this.listCategories = response.data.categories as CategoryMenu[];
            this.getMenuByCategory(this.listCategories[this.selectedCategoryIndex].name)
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error fetching news:', error);
          this.loading = false;
        }
      });
  }
  getMenuByCategory(pet: string) {
    this.service.getMenuWithCategory(pet)
      .subscribe({
        next: (response) => {
          console.log('pump 2',response)

          if (!response.error && response.data) {
            this.listProduct = response.data.result as Product[];
          }
          this.loading = false;
        },
        error: (error) => {
          console.error('Error fetching news:', error);
          this.loading = false;
        }
      });
  }

  changeCategory(i: number) {
    this.selectedCategoryIndex = i
    const pet = this.listCategories[i]._id
    this.getMenuByCategory(pet)
  }

  /**
   * Lấy danh sách cơ sở từ API
   */
  getBranchStores() {
    this.loadingBranches = true;
    this.service.getBranchStores(this.storeId)
      .subscribe({
        next: (response) => {
          console.log('Branch stores response:', response);

          if (!response.error && response.data) {
            this.branches = response.data.branchStore;
          }
          this.loadingBranches = false;
        },
        error: (error) => {
          console.error('Error fetching branch stores:', error);
          this.loadingBranches = false;
        }
      });
  }

  /**
   * Mở Google Maps để chỉ đường đến cơ sở
   * @param branch Thông tin cơ sở
   */
  openGoogleMaps(branch: BranchStore) {
    let googleMapsUrl = '';

    // Nếu có tọa độ lat/lng, sử dụng tọa độ
    if (branch.lat && branch.lng) {
      googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${branch.lat},${branch.lng}`;
    }
    // Nếu không có tọa độ, sử dụng địa chỉ
    else if (branch.address) {
      const encodedAddress = encodeURIComponent(branch.address);
      googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`;
    }

    // Mở Google Maps trong tab mới
    if (googleMapsUrl) {
      window.open(googleMapsUrl, '_blank');
    } else {
      console.error('Không có thông tin địa chỉ hoặc tọa độ để chỉ đường');
    }
  }

  /**
   * Xử lý trường hợp ảnh bị lỗi
   * @param event Event từ DOM
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;
    // Thay thế bằng ảnh placeholder SVG màu xám #e2e2e2
    imgElement.src = 'assets/images/placeholder.svg';
    // Thêm class để styling riêng nếu cần
    imgElement.classList.add('img-error');
  }
  onBookingClick() {
    const dialogRef = this.dialog.open(BookingDialogComponent, {
      width: '30%',
      maxWidth: '700px',
      disableClose: false,
      autoFocus: true,
      panelClass: 'booking-dialog-container',
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        console.log('Booking created:', result);
        // Có thể thêm logic xử lý sau khi đặt bàn thành công
      }
    });
  }
}
