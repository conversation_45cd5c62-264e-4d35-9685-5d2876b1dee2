// File: src/app/core/pipes/vnd-currency.pipe.ts
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  standalone: true,
  name: 'vndCurrency'
})
export class CurrencyPipe implements PipeTransform {
  transform(value: number | string): string {
    // Convert to number if input is string
    const num = typeof value === 'string' ? parseFloat(value) : value;

    // Return empty string for invalid numbers
    if (isNaN(num)) return '';

    // Format number with Vietnamese locale
    return num.toLocaleString('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });
  }
}
