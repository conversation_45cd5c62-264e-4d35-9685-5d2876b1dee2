<div class="booking-dialog container-fluid">
  <div class="dialog-header row">
    <div class="col-10 col-sm-11">
      <h2 mat-dialog-title class="d-flex align-items-center mb-0">
        <mat-icon class="header-icon me-2 me-md-3">restaurant</mat-icon>
        <span class="d-none d-sm-inline">LỊCH ĐẶT BÀN</span>
        <span class="d-inline d-sm-none">ĐẶT BÀN</span>
      </h2>
    </div>
    <div class="col-2 col-sm-1 text-end">
      <button mat-icon-button (click)="onCancel()" class="close-button">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <div mat-dialog-content class="dialog-content">
    <form [formGroup]="bookingForm" class="booking-form">
      <!-- Chi nhánh / Cơ sở -->
      <div class="form-row row mb-3 mb-md-4">
        <div class="col-12">
          <app-select-input
            formControlName="branchId"
            label="Chi nhánh / Cơ sở"
            placeholder="Chọn chi nhánh"
            [options]="branchOptions"
            [required]="true"
            [errorMessages]="{
              required: 'Vui lòng chọn chi nhánh'
            }"
          >
          </app-select-input>
        </div>
      </div>

      <!-- Tên của bạn -->
      <div class="form-row row mb-3 mb-md-4">
        <div class="col-12">
          <app-text-input
            formControlName="customerName"
            label="Tên của bạn"
            placeholder="Nhập họ và tên"
            [required]="true"
            [errorMessages]="{
              required: 'Vui lòng nhập tên',
              minlength: 'Tên phải có ít nhất 2 ký tự'
            }"
          >
          </app-text-input>
        </div>
      </div>

      <!-- Số điện thoại -->
      <div class="form-row row mb-3 mb-md-4">
        <div class="col-12">
          <app-text-input
            formControlName="phoneNumber"
            label="Số điện thoại"
            placeholder="Nhập số điện thoại"
            type="tel"
            [required]="true"
            [errorMessages]="{
              required: 'Vui lòng nhập số điện thoại',
              pattern: 'Số điện thoại không đúng định dạng'
            }"
          >
          </app-text-input>
        </div>
      </div>

      <!-- E-mail -->
      <div class="form-row row mb-3 mb-md-4">
        <div class="col-12">
          <app-text-input
            formControlName="email"
            label="E-mail"
            placeholder="Nhập e-mail"
            type="email"
            [required]="true"
            [errorMessages]="{
              required: 'Vui lòng nhập email',
              email: 'Email không đúng định dạng'
            }"
          >
          </app-text-input>
        </div>
      </div>

      <!-- Ngày giờ đến -->
      <div class="form-row row mb-3 mb-md-4">
        <div class="col-12">
          <app-datetime-input
            formControlName="reservationDate"
            label="Ngày giờ đến"
            placeholder="Chọn thời gian"
            [required]="true"
            [minDate]="minDate"
            [errorMessages]="{
              required: 'Vui lòng chọn ngày giờ đến'
            }"
          >
          </app-datetime-input>
        </div>
      </div>

      <!-- Số lượng người lớn và trẻ em -->
      <div class="form-row row mb-3 mb-md-4">
        <div class="col-12 col-md-6 mb-3 mb-md-0">
          <app-number-input
            formControlName="adultCount"
            label="Số lượng người lớn"
            placeholder="1"
            [min]="1"
            [max]="20"
            [required]="true"
            [errorMessages]="{
              required: 'Vui lòng nhập số lượng người lớn',
              min: 'Tối thiểu 1 người lớn'
            }"
          >
          </app-number-input>
        </div>

        <div class="col-12 col-md-6">
          <app-number-input
            formControlName="childCount"
            label="Trẻ em"
            placeholder="0"
            [min]="0"
            [max]="10"
            [errorMessages]="{
              min: 'Số lượng không được âm'
            }"
          >
          </app-number-input>
        </div>
      </div>

      <!-- Chọn suất -->
      <div class="form-row row mb-3 mb-md-4">
        <div class="col-12">
          <app-select-input
            formControlName="mealType"
            label="Chọn suất"
            placeholder="Chọn set"
            [options]="mealTypeOptions"
            [required]="true"
            [errorMessages]="{
              required: 'Vui lòng chọn suất ăn'
            }"
          >
          </app-select-input>
        </div>
      </div>

      <!-- Ghi chú -->
      <div class="form-row row mb-0">
        <div class="col-12">
          <app-text-input
            formControlName="notes"
            label="Ghi chú"
            placeholder="Nhập ghi chú"
          >
          </app-text-input>
        </div>
      </div>
    </form>
  </div>

  <div mat-dialog-actions class="dialog-actions">
    <div class="row w-100">
      <div class="col-12">
        <button
          mat-raised-button
          color="primary"
          (click)="onSubmit()"
          [disabled]="isSubmitting || isLoading"
          class="submit-button w-100"
        >
          <mat-spinner *ngIf="isSubmitting" diameter="20" class="button-spinner me-2"></mat-spinner>
          <span *ngIf="!isSubmitting">ĐẶT BÀN NGAY</span>
          <span *ngIf="isSubmitting">ĐANG XỬ LÝ...</span>
        </button>
      </div>
    </div>
  </div>
</div>
