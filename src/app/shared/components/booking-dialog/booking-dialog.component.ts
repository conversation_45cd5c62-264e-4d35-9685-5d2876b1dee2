import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { TextInputComponent } from '../form-controls/text-input/text-input.component';
import {
  SelectInputComponent,
  SelectOption,
} from '../form-controls/select-input/select-input.component';
import { DatetimeInputComponent } from '../form-controls/datetime-input/datetime-input.component';
import { NumberInputComponent } from '../form-controls/number-input/number-input.component';
import {
  BookingService,
  BookingFormData,
  BranchOption,
  MealOption,
} from '../../../core/services/booking.service';
import { BranchStore, MenuService } from '../../../core/services/menu.service';

@Component({
  selector: 'app-booking-dialog',
  templateUrl: './booking-dialog.component.html',
  styleUrls: ['./booking-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    TextInputComponent,
    SelectInputComponent,
    DatetimeInputComponent,
    NumberInputComponent,
  ],
})
export class BookingDialogComponent implements OnInit {
  bookingForm!: FormGroup;
  isLoading = false;
  isSubmitting = false;

  branchOptions: SelectOption[] = [];
  mealTypeOptions: SelectOption[] = [];
  branches: BranchStore[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<BookingDialogComponent>,
    private bookingService: BookingService,
    private menuService: MenuService,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadInitialData();
  }

  private initializeForm(): void {
    this.bookingForm = this.fb.group({
      branchId: ['', [Validators.required]],
      customerName: ['', [Validators.required, Validators.minLength(2)]],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10,11}$/)]],
      email: ['', [Validators.required, Validators.email]],
      reservationDate: ['', [Validators.required]],
      adultCount: [1, [Validators.required, Validators.min(1)]],
      childCount: [0, [Validators.min(0)]],
      mealType: ['', [Validators.required]],
      notes: [''],
    });
  }

  private loadInitialData(): void {
    this.isLoading = true;

    // Load branches từ API
    this.bookingService.getBranches().subscribe({
      next: (branchOptions: BranchOption[]) => {
        this.branchOptions = branchOptions.map(branch => ({
          value: branch.value,
          label: branch.label,
        }));

        // Load chi tiết branches để sử dụng khi submit
        this.loadBranchDetails();
      },
      error: error => {
        console.error('Error loading branches:', error);
        this.isLoading = false;
        this.snackBar.open('Không thể tải danh sách chi nhánh', 'Đóng', {
          duration: 3000,
          panelClass: ['error-snackbar'],
        });
      },
    });

    // Load meal types
    this.bookingService.getMealTypes().subscribe({
      next: (mealTypes: MealOption[]) => {
        this.mealTypeOptions = mealTypes.map(meal => ({
          value: meal.value,
          label: meal.label,
        }));
        this.isLoading = false;
      },
      error: error => {
        console.error('Error loading meal types:', error);
        this.isLoading = false;
        this.snackBar.open('Không thể tải danh sách suất ăn', 'Đóng', {
          duration: 3000,
          panelClass: ['error-snackbar'],
        });
      },
    });
  }

  private loadBranchDetails(): void {
    // Load chi tiết branches từ MenuService để có đầy đủ thông tin
    this.menuService.getBranchStores('623a9950be781e0ba814f22a').subscribe({
      next: response => {
        if (response && response.data && response.data.branchStore) {
          this.branches = response.data.branchStore;
        }
      },
      error: error => {
        console.error('Error loading branch details:', error);
      },
    });
  }

  onSubmit(): void {
    if (this.bookingForm.valid) {
      this.isSubmitting = true;

      const formValue = this.bookingForm.value;

      // Tìm branch được chọn
      const selectedBranch = this.branches.find(branch => branch._id === formValue.branchId);

      if (!selectedBranch) {
        this.isSubmitting = false;
        this.snackBar.open('Không tìm thấy thông tin chi nhánh', 'Đóng', {
          duration: 3000,
          panelClass: ['error-snackbar'],
        });
        return;
      }

      const bookingData: BookingFormData = {
        branchId: formValue.branchId,
        customerName: formValue.customerName,
        phoneNumber: formValue.phoneNumber,
        email: formValue.email,
        reservationDate: formValue.reservationDate,
        adultCount: formValue.adultCount,
        childCount: formValue.childCount,
        mealType: formValue.mealType,
        notes: formValue.notes,
      };

      this.bookingService.createBooking(bookingData, selectedBranch).subscribe({
        next: response => {
          this.isSubmitting = false;
          if (response.success && response.data) {
            this.snackBar.open('Đặt bàn thành công!', 'Đóng', {
              duration: 3000,
              panelClass: ['success-snackbar'],
            });
            this.dialogRef.close(response.data);
          } else {
            this.snackBar.open(response.message || 'Có lỗi xảy ra', 'Đóng', {
              duration: 3000,
              panelClass: ['error-snackbar'],
            });
          }
        },
        error: error => {
          this.isSubmitting = false;
          console.error('Booking error:', error);

          let errorMessage = 'Có lỗi xảy ra khi đặt bàn';
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          this.snackBar.open(errorMessage, 'Đóng', {
            duration: 3000,
            panelClass: ['error-snackbar'],
          });
        },
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.bookingForm.controls).forEach(key => {
      const control = this.bookingForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  get minDate(): Date {
    return new Date();
  }
}
