import { Component, Input, OnInit, Optional, Self } from '@angular/core';
import { ControlValueAccessor, FormControl, NgControl } from '@angular/forms';

@Component({
  template: '',
})
export abstract class BaseFormControlComponent implements ControlValueAccessor, OnInit {
  @Input() label = '';
  @Input() placeholder = '';
  @Input() hint = '';
  @Input() required = false;
  @Input() disabled = false;
  @Input() errorMessages: { [key: string]: string } = {};

  formControl: FormControl = new FormControl();
  isDisabled = false;
  touched = false;

  constructor(@Optional() @Self() public ngControl: NgControl) {
    if (this.ngControl) {
      this.ngControl.valueAccessor = this;
    }
  }

  ngOnInit(): void {
    if (this.ngControl && this.ngControl.control) {
      this.formControl = this.ngControl.control as FormControl;
    }
  }

  private onChange: (value: any) => void = () => {};

  // ControlValueAccessor methods
  writeValue(value: any): void {
    if (this.formControl.value !== value) {
      this.formControl.setValue(value, { emitEvent: false });
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
    this.formControl.valueChanges.subscribe(value => {
      this.onChange(value);
    });
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
    isDisabled ? this.formControl.disable() : this.formControl.enable();
  }

  onTouched = () => {};

  markAsTouched(): void {
    if (!this.touched) {
      this.onTouched();
      this.touched = true;
    }
  }

  // Helper method to get error message
  getErrorMessage(): string {
    if (!this.formControl.errors || !this.formControl.touched) {
      return '';
    }

    const errorType = Object.keys(this.formControl.errors)[0];
    return this.errorMessages[errorType] || `Lỗi: ${errorType}`;
  }
}
