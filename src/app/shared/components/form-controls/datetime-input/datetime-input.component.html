<mat-label>{{ label }}</mat-label>
<mat-form-field appearance="outline" class="w-100">
  <mat-icon *ngIf="prefixIcon" matPrefix>{{ prefixIcon }}</mat-icon>
  <input
    matInput
    [matDatepicker]="picker"
    [formControl]="formControl"
    [placeholder]="placeholder"
    [min]="minDate"
    [max]="maxDate"
    [disabled]="isDisabled"
    (blur)="markAsTouched()"
  />

  <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
  <mat-datepicker #picker></mat-datepicker>

  <mat-icon *ngIf="suffixIcon" matSuffix>{{ suffixIcon }}</mat-icon>

  <mat-hint *ngIf="hint">{{ hint }}</mat-hint>

  <mat-error *ngIf="formControl.invalid && formControl.touched">
    {{ getErrorMessage() }}
  </mat-error>
</mat-form-field>
