<mat-label>{{ label }}</mat-label>
<mat-form-field appearance="outline" class="w-100">
  <mat-icon *ngIf="prefixIcon" matPrefix>{{ prefixIcon }}</mat-icon>

  <input
    matInput
    ngxDaterangepickerMd
    [placeholder]="placeholder || 'Chọn ngày giờ'"
    [disabled]="isDisabled"
    [value]="displayValue"
    [singleDatePicker]="singleDatePicker"
    [autoApply]="true"
    [timePicker]="timePicker"
    [timePicker24Hour]="timePicker24Hour"
    [timePickerIncrement]="timePickerIncrement"
    [showDropdowns]="showDropdowns"
    [showWeekNumbers]="showWeekNumbers"
    [showISOWeekNumbers]="showISOWeekNumbers"
    [ranges]="ranges"
    [showClearButton]="showClearButton"
    [showCancel]="showCancel"
    [linkedCalendars]="linkedCalendars"
    [alwaysShowCalendars]="alwaysShowCalendars"
    [locale]="locale"
    [drops]="'down'"
    [opens]="'right'"
    (datesUpdated)="onDateSelected($event)"
    (cancelDaterangepicker)="onCancel()"
    (clearDaterangepicker)="onClear()"
    (blur)="markAsTouched()"
    (focus)="markAsTouched()"
    readonly
  />

  <mat-icon *ngIf="suffixIcon" matSuffix>{{ suffixIcon }}</mat-icon>

  <mat-hint *ngIf="hint">{{ hint }}</mat-hint>

  <mat-error>
    {{ getErrorMessage() }}
  </mat-error>

  <!-- Debug info - remove in production -->
  <div style="font-size: 10px; color: #999; margin-top: 4px;" *ngIf="true">
    Debug: Valid={{ formControl.valid }}, Touched={{ formControl.touched }},
    Value={{ formControl.value }}, Errors={{ formControl.errors | json }}
  </div>
  {{ getErrorMessage() | json}}
</mat-form-field>
