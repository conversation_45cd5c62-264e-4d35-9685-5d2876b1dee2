<mat-label>{{ label }}</mat-label>
<mat-form-field appearance="outline" class="w-100">
  <mat-icon *ngIf="prefixIcon" matPrefix>{{ prefixIcon }}</mat-icon>

  <input
    matInput
    type="datetime-local"
    [formControl]="formControl"
    [placeholder]="placeholder"
    [min]="minDateTimeString"
    [max]="maxDateTimeString"
    [value]="currentDateTimeString"
    [disabled]="isDisabled"
    (blur)="markAsTouched()"
    (change)="onDateTimeChange($event)"
  />

  <mat-icon *ngIf="suffixIcon" matSuffix>{{ suffixIcon }}</mat-icon>

  <mat-hint *ngIf="hint">{{ hint }}</mat-hint>

  <mat-error *ngIf="formControl.invalid && formControl.touched">
    {{ getErrorMessage() }}
  </mat-error>
</mat-form-field>
