:host {
  display: block;
  width: 100%;

  ::ng-deep .mat-mdc-form-field {
    margin-bottom: 0;

    .mat-mdc-text-field-wrapper {
      &:hover {
        border-color: #cbd5e1;
        background-color: #f1f5f9;
      }

      &.mdc-text-field--focused {
        border-color: #2563eb;
        background-color: white;
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
      }
    }

    .mat-mdc-form-field-flex {
      height: 48px;
      align-items: center;
    }

    .mat-mdc-form-field-infix {
      padding: 0;
      min-height: auto;

      .mat-mdc-select {
        font-size: 15px;
        font-weight: 500;
        color: #1f2937;
      }
    }

    .mat-mdc-form-field-icon-prefix,
    .mat-mdc-form-field-icon-suffix {
      color: #6b7280;
      margin: 0 8px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      .mat-mdc-form-field-hint,
      .mat-mdc-form-field-error {
        font-size: 13px;
        font-weight: 500;
      }

      .mat-mdc-form-field-error {
        color: #ef4444;
      }

      .mat-mdc-form-field-hint {
        color: #6b7280;
      }
    }

    .mdc-floating-label {
      font-size: 14px;
      font-weight: 600;
      color: #374151;

      &.mdc-floating-label--float-above {
        color: #2563eb;
      }
    }
  }
}

.w-100 {
  width: 100%;
}

.prefix-icon, .suffix-icon {
  display: flex;
  align-items: center;
  color: rgba(0, 0, 0, 0.54);
  margin-right: 8px;
}

.suffix-icon {
  margin-right: 0;
  margin-left: 8px;
}

// Datetime input specific styles
input[type="datetime-local"] {
  font-size: 15px;
  font-weight: 500;
  color: #1f2937;

  &::-webkit-calendar-picker-indicator {
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;

    &:hover {
      background-color: #f3f4f6;
    }
  }

  &::-webkit-datetime-edit {
    padding: 0;
  }

  &::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  &::-webkit-datetime-edit-text {
    color: #6b7280;
    padding: 0 2px;
  }

  &::-webkit-datetime-edit-month-field,
  &::-webkit-datetime-edit-day-field,
  &::-webkit-datetime-edit-year-field,
  &::-webkit-datetime-edit-hour-field,
  &::-webkit-datetime-edit-minute-field {
    padding: 0 2px;
    color: #1f2937;

    &:focus {
      background-color: #dbeafe;
      outline: none;
      border-radius: 2px;
    }
  }
}
