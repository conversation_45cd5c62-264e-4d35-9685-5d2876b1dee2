import { Component, Input, OnInit, Optional, Self } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { BaseFormControlComponent } from '../base-form-control.component';

@Component({
  selector: 'app-datetime-input',
  templateUrl: './datetime-input.component.html',
  styleUrls: ['./datetime-input.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatIconModule],
})
export class DatetimeInputComponent extends BaseFormControlComponent implements OnInit {
  @Input() minDate?: Date;
  @Input() maxDate?: Date;
  @Input() prefixIcon = '';
  @Input() suffixIcon = '';

  constructor(@Optional() @Self() public override ngControl: NgControl) {
    super(ngControl);
  }

  override ngOnInit(): void {
    super.ngOnInit();

    // Set default error messages if not provided
    if (Object.keys(this.errorMessages).length === 0) {
      this.errorMessages = {
        required: 'Vui lòng chọn ngày giờ',
        min: 'Ngày giờ không được nhỏ hơn thời gian tối thiểu',
        max: 'Ngày giờ không được lớn hơn thời gian tối đa',
      };
    }

    // Set minimum date to today if not provided
    if (!this.minDate) {
      this.minDate = new Date();
    }
  }

  get minDateTimeString(): string {
    if (!this.minDate) return '';
    return this.formatDateTimeForInput(this.minDate);
  }

  get maxDateTimeString(): string {
    if (!this.maxDate) return '';
    return this.formatDateTimeForInput(this.maxDate);
  }

  get currentDateTimeString(): string {
    const value = this.formControl.value;
    if (value instanceof Date) {
      return this.formatDateTimeForInput(value);
    }
    return '';
  }

  onDateTimeChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const value = target.value;

    if (value) {
      // Convert datetime-local string to Date object
      const dateTime = new Date(value);
      this.formControl.setValue(dateTime);
    } else {
      this.formControl.setValue(null);
    }

    this.markAsTouched();
  }

  private formatDateTimeForInput(date: Date): string {
    // Format date to YYYY-MM-DDTHH:MM format for datetime-local input
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }
}
