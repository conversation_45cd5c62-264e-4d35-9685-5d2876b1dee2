var dataNewShows = dataNews;
let currentPage = 1;
let itemsPerPage = 15;
let statusTable = 'all'
let totalPages = Math.ceil(dataNewShows.length / itemsPerPage)

function renderTable() {
    let html = '';
    let start = (currentPage - 1) * itemsPerPage;
    let end = currentPage * itemsPerPage - 1
    if (end > dataNewShows.length - 1) {
        end = dataNewShows.length
    }
    for (let i = start; i <= end; i++) {
        if (dataNewShows[i]) {
            let news = dataNewShows[i]
            if (news.status == 1) {
                buttonHtml = `
                <a href="javascript:;" class="success-news" title="Xác nhận hiện bài viết">
                    <img src="/template/ui/img/check.svg" alt="">
                </a> `
            } else {
                buttonHtml = `
                <a href="javascript:;" class="cancel-news"title="Xác nhận ẩn bài viết">
                    <img src="/template/ui/img/close.svg" alt="">
                </a> `
            }
            html += `
            <tr id="${news._id}">
                <td>${i + 1}</td>
                <td>${news.name}</td>
                <td><span class="img_product"><img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${encodeURI(news.thumbail)}" alt="" style="max-width: 40px;"></span></td>
                <td class="status"><span class="${news.status == 0 ? 'stocking' : 'out_stock'}">${news.status == 0 ? 'Đang hiện' : 'Đang ẩn'}</span></td>
                <td>${news.tag}</td>
                <td>${news.province || ''}</td>
                <td>${moment(news.modifyAt).format('HH:mm DD/MM/YYYY')}</span></td>
                <td>${news.order ? news.order: '--'}</span></td>
                <td>
                    ${buttonHtml}
                    <a href="/admin/chinh-sua-banner-store/${news._id}.html" class="edit-news" title="Chỉnh sửa">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                    <a href="javascript:;" class="delete-news" title="Xoá">
                        <img src="/template/ui/img/delete.svg" alt="">
                    </a>
                </td>
            </tr>
            `
        }
    }
    $('.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${dataNewShows.length} kết quả`)
    $('.table_portfolio .tbody-table').html(html);
    renderPagination()
}

function renderPagination() {
    let min = 1;
    let max = 1
    let html = '';
    $(".pagination_container ul").html('');
    html += '<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>'
    html += '<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>'
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPage) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPage) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage) - 2;
        max = Number(currentPage) + 2
    }
    if (min == 2) {
        html += `<li class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html += `<li class="page ${totalPages == Number(currentPage) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html += '<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>'
    html += '<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>'
    $('.pagination_container .pagination-list').html(html);
}

function filterList() {
    currentPage = 1;
    dataNewShows = []
    let data2s = []
    statusTable = $('#status-news').val()
    if (statusTable == 'all') {
        data2s = dataNews
    } else {
        dataNews.forEach(news => {
            if (news.status == statusTable) {
                data2s.push(news)
            }
        })
    }
    let key = removeUtf8($('#search-goods').val().trim())
    let data3s = []
    data2s.forEach(item => {
        if (removeUtf8(item.title).includes(key) || removeUtf8(item.description.toString()).includes(key)) {
            data3s.push(item)
        }
    })
    dataNewShows = data3s
    totalPages = Math.ceil(dataNewShows.length / itemsPerPage)
    renderTable()
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        if (currentPage > 1) {
            currentPage = currentPage - 1;
            renderTable();
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        currentPage = 1;
        renderTable();
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        currentPage = totalPages;
        renderTable();
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        if (Number(currentPage) < Number(totalPages)) {
            currentPage = currentPage + 1;
            renderTable();
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let page = e.currentTarget.value;
        currentPage = page;
        renderTable();
    });

    $(document).on('click', '.success-news', function (e) {
        let conf = confirm('Bạn chắc chắn hiển thị bài viết này?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/change-status-banner-store/${id}.html?status=0`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.cancel-news', function (e) {
        let conf = confirm('Bạn chắc chắn ẩn bài viết này?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/change-status-banner-store/${id}.html?status=1`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });

    $(document).on('click', '.delete-news', function (e) {
        let conf = confirm('Chính xác bạn có muốn xóa bài viết này?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/delete-banner-store/${id}.html`, {}, (response) => {
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    renderTable()
})

