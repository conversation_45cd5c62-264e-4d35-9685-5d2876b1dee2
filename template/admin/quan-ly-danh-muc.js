var categorieShows = categories;
let currentPage = 1;
let itemsPerPage = 15;
let statusTable = 'all'
let totalPages = Math.ceil(categorieShows.length / itemsPerPage)

function renderTable() {
    let html = '';
    let start = (currentPage - 1) * itemsPerPage;
    let end = currentPage * itemsPerPage - 1
    if (end > categorieShows.length - 1) {
        end = categorieShows.length
    }
    for (let i = start; i <= end; i++) {
        if (categorieShows[i]) {
            let category = categorieShows[i]
            category.codeDM = createMaDanhMuc(category.code)
            html += `
            <tr id="${category._id}">
                <td>${i + 1}</td>
                <td>${category.codeDM}</td>
                <td><span class="img_product"><img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${encodeURI(category.picture)}" alt="" style="max-width: 40px;"></span></td>
                <td>${category.name}</td>
                <td>${category.type == 0 ? 'Ô tô' : category.type == 1 ? 'Xe máy' : category.type == 3 ? 'Dịch vụ' :'Phương tiện khác'}</td>
                <td>${moment(Number(category.createAt)).format('HH:mm DD/MM/YYYY')}</td>
                <td>
                    <a href="/admin/chinh-sua-danh-muc/${category._id}.html" class="edit-category" title="Chỉnh sửa">
                        <img src="/template/ui/img/edit.svg" alt="">
                    </a>
                    <a href="javascript:;" class="delete-category" title="Xoá">
                        <img src="/template/ui/img/delete.svg" alt="">
                    </a>
                </td>
            </tr>
            `
        }
    }
    $('.pagination_container p').text(`Hiển thị từ ${end < 0 ? 0 : start + 1} đến ${end} trong tổng số ${categorieShows.length} kết quả`)
    $('.table_portfolio .tbody-table').html(html);
    renderPagination()
}

function renderPagination() {
    let min = 1;
    let max = 1
    let html = '';
    $(".pagination_container ul").html('');
    html += '<li class="first-page"><a href="javascript:;"><img src="/template/ui/img/arrrow-2-left.svg" alt=""></a></li>'
    html += '<li class="prev-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-left.svg" alt=""></a></li>'
    if (5 >= totalPages) {
        min = 1;
        max = totalPages
    } else if (Number(currentPage) + 2 > Number(totalPages)) {
        max = totalPages;
        min = totalPages - 4
    } else if (Number(currentPage) - 2 < 1) {
        min = 1;
        max = 5
    } else {
        min = Number(currentPage) - 2;
        max = Number(currentPage) + 2
    }
    if (min == 2) {
        html += `<li class="page" value="1"> <a href="javascript:;">1</a> </li>`
    }
    for (let i = min; i <= max; i++) {
        html += `<li class="page ${i == Number(currentPage) ? 'active' : ''}" value="${i}"> <a href="javascript:;">${i}</a> </li>`
    }
    if (max == totalPages - 2) {
        html += `<li class="page ${totalPages == Number(currentPage) ? 'active' : ''}"  value="${totalPages}"><a href="javascript:;" >${totalPages}</a></li>`
    }
    html += '<li class="next-page"><a href="javascript:;"><img src="/template/ui/img/arrow-1-right.svg" alt=""></a></li>'
    html += '<li class="last-page"><a href="javascript:;"><img src="/template/ui/img/arrow-2-right.svg" alt=""></a></li>'
    $('.pagination_container .pagination-list').html(html);
}

function filterList() {
    currentPage = 1;
    categorieShows = []
    statusTable = $('.portfolio_select').val()
    if (statusTable == 'all') {
        categorieShows = categories
    } else {
        categories.forEach(category => {
            if (category.type == statusTable) {
                categorieShows.push(category)
            }
        })
    }
    let key = removeUtf8($('#search-goods').val().trim())
    let dataUsers = []
    categorieShows.forEach(item => {
        if (removeUtf8(item.name).includes(key) || removeUtf8(item.codeDM.toString()).includes(key)) {
            dataUsers.push(item)
        }
    })
    let timeFilter = $('#time-filter').val()
    let dataUsers2 = []
    if (!isNaN(new Date(timeFilter).getTime())) {
        dataUsers.forEach(item => {
            if (item.createAt >= new Date(timeFilter).getTime()) {
                dataUsers2.push(item)
            }
        })
    } else {
        dataUsers2 = dataUsers
    }
    categorieShows = dataUsers2
    totalPages = Math.ceil(categorieShows.length / itemsPerPage)
    renderTable()
}

$(() => {
    $(document).on('click', '.prev-page', function (e) {
        e.preventDefault()
        if (currentPage > 1) {
            currentPage = currentPage - 1;
            renderTable();
        }
    });
    $(document).on('click', '.first-page', function (e) {
        e.preventDefault()
        currentPage = 1;
        renderTable();
    });
    $(document).on('click', '.last-page', function (e) {
        e.preventDefault()
        currentPage = totalPages;
        renderTable();
    });
    $(document).on('click', '.next-page', function (e) {
        e.preventDefault()
        if (Number(currentPage) < Number(totalPages)) {
            currentPage = currentPage + 1;
            renderTable();
        }
    });
    $(document).on('click', '.page', function (e) {
        e.preventDefault();
        let page = e.currentTarget.value;
        currentPage = page;
        renderTable();
    });
    $(document).on('click', '.delete-category', function (e) {
        let conf = confirm('Bạn có chắc muốn xóa yêu cầu này?');
        let id = $(this).parents('tr').attr('id')
        if (conf) {
            get(`/admin/delete-category/${id}.html`, {}, (response) => {
                debugger
                if (!response.error) {
                    location.reload()
                } else {
                    displayError(response.message);
                }
            })
        }
    });
    renderTable()
})

