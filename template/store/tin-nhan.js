var userSelect = {};
var messageShows = [];

let findSizeImg = function (e) {
    let maxHeight = Number($('.content_chat').height()) - 20;

    if (e.width == -1) return {width: maxHeight, height: maxHeight}
    if (e.height < maxHeight) {
        return {width: e.width, height: e.height}
    } else {
        return {width: maxHeight * e.width / e.height, height: maxHeight}
    }
};

function selectUserChat() {
    $('.title_box').html(`
		<img src="${userSelect.userPicture ? userSelect.userPicture : ''}" alt=""> ${userSelect.fullName ? userSelect.fullName : ''}
		`)
    messageShows = []
    messages.forEach(item => {
        if (item.userSendId == userSelect.userId || item.userId == userSelect.userId) {
            messageShows.push(item)
        }
    })
    messageShows.sort((a, b) => (a.createAt > b.createAt) ? 1 : ((b.createAt > a.createAt) ? -1 : 0));
    let html = ''
    messageShows.forEach(item => {
        let content = ''
        if (item.type == 0) {
            content = `<p>${item.content}</p>`
        } else if (item.type == 1) {
            content = `<img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${item.picture}" alt="" style="width: ${findSizeImg(item).width}px; height: ${findSizeImg(item).height}px; object-fit: contain ">`
        }
        html += `
			<li class="text-left ${item.userId == userLogin._id ? 'customer' : 'host'}">
				<div class="img_chat">
					<img style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover" src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${item.userId == userLogin._id ? item.userPicture : userLogin.picture}" alt="">
				</div>
				<div class="text_chat" style="background: ${item.type == 1 ? 'none' : 'null'}">
					${content}
				</div>
				<div class="time_chat">
					${new Date(item.createAt).setHours(0, 0, 0, 0) == new Date().setHours(0, 0, 0, 0) ? moment(item.createAt).format('HH:mm') : moment(item.createAt).format('HH:mm DD/MM/YYYY')}
				</div>
			</li>
			`
    })
    $('.content_chat ul').html(html)
    $(".content_chat").animate({scrollTop: $('.content_chat').prop("scrollHeight")}, 500);
}

function watchedMessage(userId) {
    get(`/user/api/xem-tin-nhan.html/${userId}`, {}, (res) => {
    });
}

function changeSelect(userShowId) {
    listUsers.forEach((item, index) => {
        if (item.userId == userShowId || item.userSendId == userShowId) {
            listUsers[index].watched = 1
            userSelect = {
                userId: userShowId,
                fullName: item.fullName,
                userPicture: item.userPicture,
            }
        }
    });
    if ($(`#${userShowId} a`).hasClass('no-read')) {
        watchedMessage(userShowId)
    }
    renderListUsers();
    selectUserChat();

    // kiểm tra trạng thái tin nhắn
    checkUserChatStatus(userShowId);

}

function checkUserChatStatus(userId) {
    get(`/store/kiem-tra-trang-thai-tin-nhan-user.html/${userId}`, {}, (res) => {
        let nhanTinNhanTuNguoiKhac = res.data.nhanTinNhanTuNguoiKhac;
        if (nhanTinNhanTuNguoiKhac == 0) {
            $('.form_enter_chat').hide();
            $('.form_disable_chat').show();
        } else {
            $('.form_enter_chat').show();
            $('.form_disable_chat').hide();
        }
    });
}

function filterListUsers() {
    let key = removeUtf8($('#search-message').val())
    listUsers.forEach(item => {
        let userShowId = item.userId == userLogin._id ? item.userSendId : item.userId
        if (removeUtf8(item.fullName).includes(key) || removeUtf8(item.content).includes(key)) {
            $(`#${userShowId}`).show()
        } else {
            $(`#${userShowId}`).hide()
        }
    })
}

renderListUsers()

function renderListUsers() {
    let html = ''
    rooms.forEach((item, index) => {
        let content = ''
        if (item.type == 0) {
            content = `${item.content}`
        } else if (item.type == 1) {
            content = 'Đã gửi 1 hình ảnh'
        }
        let userShowId = item.userId == userLogin._id ? item.userSendId : item.userId
        html += `
			<li class="chat_item" id="${userShowId}">
				<a href="javascript:;" onclick="changeSelect('${userShowId}')" class="${userShowId == userSelect.userId ? 'active' : ''} ${item.userSendId == userLogin._id ? '' : item.watched == 0 ? 'no-read' : ''}">
					<div class="img">
						<img style="width: 40px;border-radius: 50%;" src="${item.userPicture}" alt="">
					</div>
					<div class="content_chat_item">
						<div class="name_chat">
							<div class="name">
								${item.fullName}
							</div>
							<span class="time_chat">${new Date(item.createAt).setHours(0, 0, 0, 0) == new Date().setHours(0, 0, 0, 0) ? moment(item.createAt).format('HH:mm') : moment(item.createAt).format('HH:mm DD/MM/YYYY')}</span>
						</div>
						<div class="text_chat">
							${content}
						</div>
					</div>
				</a>
			</li>
			`
    })
    $('.list_chat_container ul').html(html)
    filterListUsers()
}

function sendMessage() {
    if ($('.emoji-wysiwyg-editor').html().toString().trim().length > 0 && userSelect.userId) {
        socket.emit('realtime-chat', {
            to: userSelect.userId,
            type: 'new-message',
            content: $('.emoji-wysiwyg-editor').html().toString().trim()
        });
        $('#send-message').val('')
        $('.emoji-wysiwyg-editor').html('')
    }
}

function resetCountMessage() {
    get('/user/api/reset-count-message.html', {}, (res) => {
    });
    $('#number-message').hide()
    $('#number-message').text(0)
}

$(document).on('keypress', '.emoji-wysiwyg-editor, #send-message', function (e) {
    if (e.which == 10 || e.which == 13) {
        sendMessage()
        return false
    }
});

resetCountMessage()

$(document).on('change', '#imgupload', function (e) {
    let formData = new FormData();
    for (let i = 0; i < $(`#imgupload`).prop('files').length; i++) {
        if ($(`#imgupload`).prop('files')[i]) {
            formData.append(`picture`, $(`#imgupload`).prop('files')[i])
        }
    }

    ajaxFile(`/store/gui-tin-nhan-anh.html/${userSelect.userId}`, formData, (res) => {
        if (!res.error) {
            $('#imgupload').val('')
        } else {
            displayError(res.message);
        }
    })
});

$(document).on('change keyup', '#search-message', function (e) {
    filterListUsers()
});
