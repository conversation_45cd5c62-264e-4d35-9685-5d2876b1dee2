"use strict";
const multer = require('multer');
const fileUtils = require('../utils/FileUtils');
const UPLOAD_CENTRALIZE = '/UPLOAD-CENTRALIZE';
const BASE_DIR = require('../../app').BASE_DIR;
const multerGoogleStorage = require("multer-cloud-storage");

module.exports = function (arrayUpload) {

    let upload = null;

    if (arrayUpload[0].name == 'excelFile') { // Upload to local for Excel File
        upload = multer({
            storage: multer.diskStorage({
                destination: function (req, file, cb) {
                    let mimetype = file.mimetype;
                    let folder = 'files/' + mimetype.split("/")[0] + "s/";
                    const centralize = req.url.toUpperCase();
                    if (centralize === UPLOAD_CENTRALIZE) {
                        folder = 'files/' + req.user._id + "/";
                    }
                    fileUtils.checkAndCreateFolder(BASE_DIR + "/" + folder, () => {
                        cb(null, folder);
                    });
                },
                filename: (req, file, cb) => {
                    cb(null, file.fieldname + "-" + Date.now() + "-" + file.originalname)
                }
                // filename: function (req, file, cb) {
                //     let originalnameArr = file.mimetype.split("/");
                //     let newFileName = `${stringUtils.md5(file.originalname + stringUtils.randomString() + new Date().getTime())}.${originalnameArr[originalnameArr.length - 1]}`;
                //     cb(null, newFileName);
                // }
            })
        });
    } else {

        // Upload to google storage
        upload = multer({
            storage: multerGoogleStorage.storageEngine({
                email: '<EMAIL>',
                projectId: 'lnbvd-75473',
                bucket: 'lnbvd-75473.firebasestorage.app',
                uniformBucketLevelAccess: true,
                keyFilename: './lnbvd-75473-firebase-adminsdk-fbsvc-bb1510153f.json',
                destination: function (req, file, cb) {
                    let mimetype = file.mimetype;
                    let folder = '/files/' + mimetype.split("/")[0] + "s/";
                    const centralize = req.url.toUpperCase();
                    if (centralize === UPLOAD_CENTRALIZE) { // FOR IMPORT PRODUCT UPLOAD
                        folder = 'files/' + req.user._id + '/';
                        cb(null, folder);
                    } else if (centralize === `/UPDATE-AVATAR.HTML?OUTPUT=JSON`) { // FOR API UPLOAD AVATAR
                        folder = '/files/avatar/' + req.user._id + '/';
                        cb(null, folder);
                    } else { // FOR ALL UPLOAD
                        cb(null, folder);
                    }
                },
                filename: function (request, file, callback) {
                    const centralize = request.url.toUpperCase();
                    const fileName = file.originalname;
                    if (centralize === UPLOAD_CENTRALIZE || centralize === `/UPDATE-AVATAR.HTML?OUTPUT=JSON`) { // FOR IMPORT PRODUCT UPLOAD
                        callback(null, fileName);
                    } else { // FOR ALL UPLOAD
                        callback(null, file.fieldname + "-" + Date.now() + "-" + fileName);
                    }

                }
            })
        });
    }

    let config = [];
    arrayUpload.forEach(a => {
        config.push({
            name: a.name,
            maxCount: a.maxCount,
        })
    });

    return upload.fields(arrayUpload);
};
