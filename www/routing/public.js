"use strict";

const ObjectId = require('mongoose').Types.ObjectId;
const ChildRouter = require('../config/router/ChildRouting');
const UserModel = require('../models/UserModel');
const BookSpaModel = require('../models/BookSpaModel');
const BookExaminationModel = require('../models/BookExaminationModel');
const BookRoomModel = require('../models/BookRoomModel');
const RoomOfHotelModel = require('../models/RoomOfHotelModel');
const ServiceOfSpaModel = require('../models/ServiceOfSpaModel');
const CfJws = require('../config/CfJws');
const UserSession = require('../session/UserSession');
const MailUser = require('../mailer/module/MailUser');
const StringUtils = require('../utils/StringUtils');
const CodePasswordModel = require('../models/CodePasswordModel');
const AccountBankModel = require('../models/AccountBankModel');
const NotificationModel = require('../models/NotificationModel');
const FcmTokensModel = require('../models/FcmTokensModel');
const VpnPayModel = require('../models/VpnPayModel');
const ServicesModel = require('../models/ServicesModel');
const BranchModel = require('../models/BranchModel');
const ShippingModel = require('../models/ShippingModel');
const CfVpnPay = require('../config/CfVpnPay');
const querystring = require('qs');
const CodeEmailModel = require('../models/CodeEmailModel');
const WalletOnlineModel = require('../models/WalletOnlineModel');
const sha256 = require('sha256');
const TimeUtils = require('../utils/TimeUtils');
const XLSX = require('xlsx-style');
const FileUtils = require('../utils/FileUtils');
const NumberUtils = require('../utils/NumberUtils');
const PointUtil = require('../utils/PointUtil');
const MapUtil = require('../utils/MapUtil');
const ServiceUpdateData = require('../utils/ServiceUpdateData');
const xl = require('excel4node');
const _ = require('lodash');
const requestIp = require('request-ip');
const moment = require('moment');
const APP = require('../../app');
const {
    firebaseAdmin
} = require("../config/CfFirebaseAdmin");
var numeral = require('numeral');
const CfApp = require("../config/CfApp");
const BookingUtil = require("../utils/BookingUtil");
const ReportUtil = require("../utils/ReportUtil");
const {TypeServices} = require("../models/enum/TypeServices");
const RequestBuyProductModel = require('../models/RequestBuyProductModel');
const axios = require("axios");
const {apiUrlV2} = require("../config/CfApp");
const Notification = require("../utils/Notification");
const SettingAdminModel = require("../models/SettingAdminModel");
const {sortObject} = require("../utils/StringUtils");
const {ProductOrderStatus} = require("../models/enum/ProductOrderStatus");
const crypto = require("crypto");

module.exports = class Auth extends ChildRouter {

    constructor() {
        super('/');
    }

    registerRouting(io) {
        return {
            '/': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/trang-chu.ejs',
                    get: 'view',
                    title: 'Trang chủ',
                },


                methods: {
                    get: [async function (req, res) {
                        // if (req.user && (req.user.type == 1 || req.user.type == 0)) {
                        //     return ChildRouter.redirect(res, req.user.type == 0 ? '/admin' : '/store')
                        // }
                        let rs;
                        try {
                            rs = await axios.get(`${apiUrlV2}/api/web-trang-chu?populate=*`, {
                                headers: {
                                    "Content-Type": 'application/json'
                                }
                            });
                        } catch (error) {
                            console.log('Error fetching web-trang-chu data:', error.message);
                            // Continue with empty data if API fails
                            return ChildRouter.renderOrResponse(req, res, { data: {} });
                        }

                        if (rs.status == 200) {
                            return ChildRouter.renderOrResponse(req, res, { data: rs?.data?.data?.attributes });
                        }
                        
                        // Fallback if response status is not 200
                        return ChildRouter.renderOrResponse(req, res, { data: {} });
                    }],
                },
            },

            '/dang-nhap.html': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/dang-nhap.ejs',
                    title: 'Đăng nhập',
                    get: 'view',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [{
                                key: 'email',
                                type: this.dataType.string,
                                name: 'Email',
                                min: 5,
                                max: 200
                            },
                                {
                                    key: 'password',
                                    type: this.dataType.string,
                                    name: 'Mật khẩu',
                                    min: 5,
                                    max: 50
                                },
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        if (req.user) {
                            return ChildRouter.redirect(res, '/');
                        }

                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {
                            email,
                            password
                        } = req.body;
                        let rsl = await UserModel.MODEL.signInAccount(email, password);
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        if (rsl.user.status === 2) {
                            return ChildRouter.responseError("Tài khoản đã bị khóa", res)
                        }

                        if (rsl.user.type === 2) {
                            return ChildRouter.responseError("Người dùng vui lòng đăng nhập trên app", res)
                        }

                        if (rsl.user.confirmEmail == 0) {
                            return ChildRouter.responseError("Email đăng ký chưa được xác nhận.", res)
                        }

                        let user = rsl.user;
                        if (req.version === 'api') {
                            let token = CfJws.createToken(user);
                            return ChildRouter.responseSuccess("Đăng nhập thành công", res, {
                                user,
                                token
                            });
                        } else {
                            UserSession.saveUser(req.session, user);
                            let redirect = '';
                            if (user.type == 1) {
                                redirect = '/thong-tin-tai-khoan.html'
                            } else if (user.type == 0) {
                                redirect = '/admin/cua-hang.html'
                            }
                            return ChildRouter.responseSuccess("Đăng nhập thành công", res, {
                                redirect
                            });

                        }
                    }]
                },
            },

            '/dang-ky.html': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/dang-ky.ejs',
                    get: 'view',
                    title: 'Đăng ký',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [{
                                key: 'fullName',
                                type: this.dataType.string,
                                name: 'Họ và tên',
                                min: 5,
                                max: 50
                            },
                                {
                                    key: 'email',
                                    type: this.dataType.string,
                                    name: 'Email',
                                    min: 5,
                                    max: 50
                                },
                                {
                                    key: 'password',
                                    type: this.dataType.string,
                                    name: 'Mật khẩu',
                                    min: 5,
                                    max: 50
                                },
                                {
                                    key: 'address',
                                    type: this.dataType.string,
                                    name: 'Địa chỉ',
                                    min: 5,
                                    max: 50
                                },
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {
                            fullName,
                            email,
                            password,
                            address,
                            cuaHang,
                            phongKham,
                            khachSan, // hotel để làm sau
                            parking,
                            spa,
                            showroom,
                            gas
                        } = req.body;
                        if (!cuaHang && !phongKham && !khachSan && !spa && !parking) return ChildRouter.responseError("Vui lòng chọn ít nhất một hình thức kinh doanh", res);
                        let rsl = await UserModel.MODEL.registerUser({
                            fullName,
                            email,
                            password,
                            address,
                            type: 1,
                            servicesStore: cuaHang ? 0 : 1,
                            servicesExamination: phongKham ? 0 : 1,
                            servicesHotel: khachSan ? 0 : 1, // hotel để làm sau
                            servicesSpa: spa ? 0 : 1,
                            servicesParking: parking ? 0 : 1,
                            servicesShowroom: showroom ? 0 : 1,
                            servicesGas: gas ? 0 : 1,
                        });
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        return ChildRouter.responseSuccess("Đăng ký thành công. Vui lòng kiểm tra email và xác nhận đăng ký tài khoản", res, {
                            redirect: '/dang-ky-thanh-cong.html?email=' + email
                        });
                    }]
                },
            },
            '/captcha.html': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/captcha.ejs',
                    get: 'view',
                    title: 'Captcha',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderToView(req, res, {});
                    }]
                },
            },
            '/dang-ky-thanh-cong.html': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/dang-ky-thanh-cong.ejs',
                    get: 'view',
                    title: 'Đăng ký tài khoản thành công',
                },

                methods: {
                    get: [async function (req, res) {
                        let {
                            email
                        } = req.query;
                        let user = await UserModel.MODEL.getDataWhere({
                            email,
                            type: 1
                        }, UserModel.MODEL.FIND_ONE());
                        if (!user) {
                            return ChildRouter.redirect(res, "/");
                        }
                        if (user.confirmEmail == 1) return ChildRouter.redirect(res, "/dang-nhap.html");

                        return ChildRouter.renderToView(req, res, {
                            email
                        });
                    }]
                },
            },

            '/xac-nhan-tai-khoan-email.html': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/xac-nhan-tai-khoan-email.ejs',
                    get: 'view',
                    title: 'Xác nhận EMAIL',
                },

                methods: {
                    get: [async function (req, res) {
                        let {
                            email,
                            code
                        } = req.query;
                        let dataCode = await CodeEmailModel.MODEL.getCode(email, code);
                        if (dataCode) {
                            await UserModel.MODEL.updateWhereClause({
                                email
                            }, {
                                confirmEmail: 1
                            });
                            return ChildRouter.renderToView(req, res, {
                                status: 1
                            });
                        } else {
                            return ChildRouter.renderToView(req, res, {
                                status: 0
                            });
                        }
                    }]
                },
            },

            '/lay-lai-mat-khau.html': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/lay-lai-mat-khau.ejs',
                    get: 'view',
                    title: 'Lấy lại mật khẩu',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [{
                                key: 'password',
                                type: this.dataType.string,
                                name: 'Mật khẩu',
                                min: 5,
                                max: 50
                            },
                                {
                                    key: 'confirmPassword',
                                    type: this.dataType.string,
                                    name: 'Nhập lại mật khẩu',
                                    min: 5,
                                    max: 200
                                },
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let {
                            email,
                            code
                        } = req.query;
                        let dataCode = await CodePasswordModel.MODEL.getCode(email, code);
                        if (dataCode) {
                            if (new Date().getTime() < (new Date(dataCode.createAt).getTime() + (60 * 24 * 60 * 1000))) {
                                return ChildRouter.renderOrResponse(req, res, {
                                    email
                                });
                            } else {
                                return ChildRouter.redirect(res, `/quen-mat-khau.html`);
                            }
                        } else {
                            return ChildRouter.redirect(res, `/quen-mat-khau.html`);
                        }
                    }],

                    post: [async function (req, res) {
                        let {
                            password,
                            confirmPassword,
                            email
                        } = req.body;
                        if (password != confirmPassword) {
                            return ChildRouter.responseError("Nhập lại mật khẩu không đúng", res);
                        }
                        let rsl = await UserModel.MODEL.updateUserByEmail(email, {
                            password
                        });
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        return ChildRouter.responseSuccess("Thay đổi thành công", res);
                    }]
                },
            },

            '/quen-mat-khau.html': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/quen-mat-khau.ejs',
                    get: 'view',
                    title: 'Quên mật khẩu',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [{
                                key: 'email',
                                type: this.dataType.string,
                                name: 'Email',
                                min: 5,
                                max: 200
                            },]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {
                            email
                        } = req.body;
                        let user = await UserModel.MODEL.getOneUsersByCondition({
                            email
                        });
                        if (user && user.type != 2) {
                            let code = await CodePasswordModel.MODEL.randomCodeEmail(user._id, user.email, 132);
                            await MailUser.sendEmailResetPassword(user.email, '/lay-lai-mat-khau.html?email=' + user.email + '&code=' + code);
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Email không tồn tại trong hệ thống!', res);
                        }
                    }]
                },
            },

            '/logout.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'view',
                },

                methods: {
                    get: [async function (req, res) {
                        req.session.destroy();
                        return ChildRouter.redirect(res, "/dang-nhap.html");
                    }],

                    post: [async function (req, res) {
                        if (req.user && req.user._id) {
                            let token = req.body.token;
                            await FcmTokensModel.MODEL.removeToken(token);
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/thong-tin-tai-khoan.html': {
                config: {
                    auth: [this.roles.account],
                    view: 'public/index.ejs',
                    inc: 'inc/thong-tin-tai-khoan.ejs',
                    get: 'view',
                    title: 'Thông tin tài khoản',
                    upload: [{
                        name: 'picture',
                        maxCount: 1
                    }],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [{
                                key: 'fullName',
                                type: this.dataType.string,
                                name: 'Họ và tên',
                                min: 5,
                                max: 200
                            },
                                {
                                    key: 'address',
                                    type: this.dataType.string,
                                    name: 'Địa chỉ',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'phone',
                                    type: this.dataType.string,
                                    name: 'Số điện thoại',
                                    min: 5,
                                    max: 200
                                },
                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        let accountBanks = await AccountBankModel.MODEL.getBankByCondition({
                            userId: req.user._id
                        })
                        return ChildRouter.renderOrResponse(req, res, {
                            accountBanks
                        });
                    }],

                    post: [async function (req, res) {
                        let {
                            fullName,
                            birthday,
                            phone,
                            address,
                            sex,
                            description
                        } = req.body;
                        let obj = {
                            fullName,
                            phone,
                            address,
                            sex: Number(sex || 1),
                            description
                        };

                        if (req.upload && req.upload.picture) {
                            obj.picture = req.upload.picture[0].path
                        }
                        if (!isNaN(new Date(birthday).getTime())) {
                            obj.birthday = new Date(birthday).getTime()
                        }
                        let rsl = await UserModel.MODEL.updateUser(req.user._id, obj);
                        if (rsl.error) {
                            return ChildRouter.responseError(rsl.message, res);
                        }
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        delete user.password;
                        if (req.version === 'api') {
                            let token = CfJws.createToken(user);
                            return ChildRouter.responseSuccess("Thành công", res, {
                                user,
                                token
                            });
                        } else {
                            UserSession.saveUser(req.session, user);
                            return ChildRouter.responseSuccess("Thành công", res);
                        }
                    }]
                },
            },

            '/thong-tin-tai-khoan-doi-mat-khau.html': {
                config: {
                    auth: [this.roles.account],
                    view: 'public/index.ejs',
                    title: 'Thông tin tài khoản',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [{
                                key: 'password',
                                type: this.dataType.string,
                                name: 'Mật khẩu hiện tại',
                                min: 5,
                                max: 50
                            },
                                {
                                    key: 'passwordNew',
                                    type: this.dataType.string,
                                    name: 'Nhập mật khẩu hiện tại',
                                    min: 5,
                                    max: 50
                                },
                                {
                                    key: 'passwordCFNew',
                                    type: this.dataType.string,
                                    name: 'Xác nhận mật khẩu mới',
                                    min: 5,
                                    max: 50
                                },
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            password,
                            passwordNew,
                            passwordCFNew
                        } = req.body;
                        let user = await UserModel.MODEL.getUsersById(req.user._id)
                        if (StringUtils.md5(password) != user.password) {
                            return ChildRouter.responseError('Mật khẩu hiện tại không đúng', res);
                        }
                        if (passwordNew != passwordCFNew) {
                            return ChildRouter.responseError('Nhập lại mật khẩu không giống', res);
                        }
                        await UserModel.MODEL.updateUser(req.user._id, {
                            password: passwordNew
                        });
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/them-tai-khoan-ngan-hang.html': {
                config: {
                    auth: [this.roles.account],
                    view: 'public/index.ejs',
                    inc: 'inc/them-tai-khoan-ngan-hang.ejs',
                    get: 'view',
                    title: 'Cài đặt',
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [{
                                key: 'fullName',
                                type: this.dataType.string,
                                name: 'Họ và tên',
                                min: 0,
                                max: 100
                            },
                                {
                                    key: 'bankName',
                                    type: this.dataType.string,
                                    name: 'Tên ngân hàng',
                                    min: 0,
                                    max: 100
                                },
                                {
                                    key: 'bankNumber',
                                    type: this.dataType.string,
                                    name: 'Số tài khoản',
                                    min: 0,
                                    max: 100
                                },
                                {
                                    key: 'bankBrnach',
                                    type: this.dataType.string,
                                    name: 'Chi nhánh',
                                    min: 0,
                                    max: 100
                                },

                            ]
                        },
                    },
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],

                    post: [async function (req, res) {
                        let {
                            fullName,
                            bankName,
                            bankNumber,
                            bankBrnach
                        } = req.body;
                        await AccountBankModel.MODEL.addBank({
                            userId: req.user._id,
                            fullName,
                            bankName,
                            bankNumber,
                            bankBrnach
                        })
                        return ChildRouter.responseSuccess("Thành công", res);
                    }]
                },
            },

            '/delete-account-bank/:accountId.html': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                    title: 'Cài đặt',
                },

                methods: {
                    get: [async function (req, res) {
                        await AccountBankModel.MODEL.deleteBank(req.params.accountId)
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/quan-ly-thong-bao.html': {
                config: {
                    auth: [this.roles.account],
                    view: 'public/index.ejs',
                    inc: 'inc/quan-ly-thong-bao.ejs',
                    get: 'view',
                    title: 'Quản lý thông báo',
                },

                methods: {
                    get: [async function (req, res) {
                        return ChildRouter.renderOrResponse(req, res);
                    }],
                },
            },

            '/lay-du-lieu-thong-bao.html': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let where = {
                            userId: req.user._id
                        };
                        let {page, typeNotify, limit} = req.query;
                        const currentPage = page ? Number(page) : 1;
                        limit = Number(limit) || 10;
                        if (req.user.type == 2) {
                            let {
                                lastId
                            } = req.query;

                            if (lastId && lastId != '') {
                                where['_id'] = {
                                    $lt: ObjectId(lastId)
                                }
                            }
                        }
                        if (typeNotify && Number(typeNotify) > 0) {
                            where['typeNotify'] = Number(typeNotify)
                        }
                        let {
                            totalPage,
                            countUnread
                        } = await NotificationModel.MODEL.getTotalPageAndUnread(where, limit);
                        let notifications = await NotificationModel.MODEL.getAllNotificationsByUserId(where, {
                            createAt: -1
                        }, currentPage, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            notifications,
                            totalPage,
                            countUnread
                        });
                    }],
                },
            },

            '/da-xem-thong-bao/:notificationId.html': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await NotificationModel.MODEL.updateNotification(req.params.notificationId, {
                            watched: 1
                        });

                        let userId = req.user._id
                        let countUnread = await NotificationModel.MODEL.countDataWhere({userId: userId, watched: 0});
                        await UserModel.MODEL.updateById(userId, {notifications: countUnread});
                        return ChildRouter.responseSuccess("Thành công", res, { countUnread });
                    }],
                },
            },

            '/update-user-fcm-token.html': {
                config: {
                    auth: [this.roles.account],
                    post: 'json'
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            token
                        } = req.body;
                        const rs = await FcmTokensModel.MODEL.getOneByCondition( { userId: req.user._id, token})
                        if (rs && rs._id)
                        {
                            return ChildRouter.responseError("Token Already", res);
                        } else {
                            await FcmTokensModel.MODEL.addToken(req.user._id, token);
                            return ChildRouter.responseSuccess("TC", res);
                        }
                    }]
                },
            },

            '/vnpay_return': {
                config: {
                    auth: [this.roles.all],
                    view: 'pages/vnpay-result.ejs',
                    get: 'view',
                    title: 'Kết quả thanh toán',
                },

                methods: {
                    get: [async function (req, res) {
                        try {
                            let vnp_Params = req.query;

                            let settingAd = await SettingAdminModel.MODEL.getSettingAdmin();
                            let secretKey = settingAd.vnpHashSecret;


                            var secureHash = vnp_Params['vnp_SecureHash'];

                            delete vnp_Params['vnp_SecureHash'];
                            delete vnp_Params['vnp_SecureHashType'];

                            vnp_Params = sortObject(vnp_Params);

                            var signData = querystring.stringify(vnp_Params, { encode: false });
                            var crypto = require("crypto");
                            var hmac = crypto.createHmac("sha512", secretKey);
                            var signed = hmac.update(new Buffer(signData, 'utf-8')).digest("hex");

                            try {
                                let db = firebaseAdmin.database()
                                let logQuery = req.query
                                let currentDateString = moment(new Date()).format('DD-MM-YYYY')
                                let docRef = db.ref(`payment/${currentDateString}/${vnp_Params['vnp_TxnRef']}/vnpay_return`);
                                logQuery['vnp_ReturnUrl'] = `${CfApp.domain}/vnpay_return?${new URLSearchParams(req.query).toString()}`
                                docRef.update(vnp_Params);
                            } catch (e) {
                                console.log(e)
                            }

                            if(secureHash === signed){

                                let message = '';
                                let status = 0;
                                let vnp_TxnRef = ''
                                let vnp_Amount = ''
                                let vnp_OrderInfo = ''
                                let vnp_PayDate = ''
                                vnp_TxnRef = vnp_Params['vnp_TxnRef']
                                vnp_Amount = vnp_Params['vnp_Amount']
                                vnp_OrderInfo = vnp_Params['vnp_OrderInfo']
                                vnp_PayDate = moment(vnp_Params['vnp_PayDate'], 'YYYYMMDDHHmmss').format('hh:mm:ss DD-MM-YYYY')

                                //Kiem tra xem du lieu trong db co hop le hay khong va thong bao ket qua
                                if (vnp_Params['vnp_ResponseCode'] == '00') {
                                    status = 1;
                                    message = 'Gửi thanh toán thành công. Ứng dụng sẽ cập nhật kết quả thanh toán khi có phản hồi từ ngân hàng.';
                                } else {
                                    message = 'Gửi thanh toán không thành công.' + CfVpnPay.codePay[vnp_Params['vnp_ResponseCode']] || '';
                                }
                                vnp_Amount = numeral(parseInt(vnp_Params['vnp_Amount']) / 100).format('0,0');
                                return ChildRouter.renderToView(req, res, {
                                    status,
                                    message,
                                    vnp_TxnRef,
                                    vnp_Amount,
                                    vnp_OrderInfo,
                                    vnp_PayDate
                                })
                            } else {
                                return ChildRouter.response(res, {
                                    Code: '97',
                                });
                            }
                        } catch (e) {

                        }

                    }]
                },
            },

            '/vnpay_ipn': {
                config: {
                    auth: [this.roles.all],
                    get: 'json'
                },

                methods: {
                    get: [async function (req, res) {
                        try {
                            let vnp_Params = req.query;
                            let secureHash = vnp_Params['vnp_SecureHash'];
                            delete vnp_Params['vnp_SecureHash'];
                            delete vnp_Params['vnp_SecureHashType'];
                            vnp_Params = sortObject(vnp_Params);

                            let settingAd = await SettingAdminModel.MODEL.getSettingAdmin();
                            let secretKey = settingAd.vnpHashSecret;
                            let signData = querystring.stringify(vnp_Params, { encode: false });
                            var crypto = require("crypto");
                            var hmac = crypto.createHmac("sha512", secretKey);
                            var signed = hmac.update(new Buffer(signData, 'utf-8')).digest("hex");

                            let amount = vnp_Params['vnp_Amount']
                            let orderInfo = vnp_Params['vnp_OrderInfo']
                            let orderId = vnp_Params['vnp_TxnRef'];
                            let bookDetail = await VpnPayModel.MODEL.getVpnPayWhere({
                                orderId
                            });
                            if (!bookDetail) {
                                // console.log('Không tìm tháy mã đơn hàng để update VNPAY')
                                return ChildRouter.responsePayment(req, res, {
                                    RspCode: '01',
                                    Message: 'Order not found'
                                });
                            }
                            let userId = bookDetail.userId

                            if (secureHash === signed) {

                                let code = vnp_Params['vnp_ResponseCode']
                                let checkStatus = await VpnPayModel.MODEL.getVpnPayWhere({
                                    userId,
                                    orderId
                                });

                                if (code == '00') {
                                    // khong tim thay don hang
                                    if (!checkStatus) {
                                        return ChildRouter.responsePayment(req, res, {
                                            RspCode: '01',
                                            Message: 'Order not found'
                                        });
                                    }

                                    // check amount
                                    if (checkStatus && (parseInt(checkStatus.amount) / 100) != (parseInt(amount) / 100)) {
                                        return ChildRouter.responsePayment(req, res, {
                                            RspCode: '04',
                                            Message: 'Invalid Amount'
                                        });
                                    }

                                    // status = 1 la da confirm
                                    if (checkStatus && checkStatus.status == '1') {
                                        return ChildRouter.responsePayment(req, res, {
                                            RspCode: '02',
                                            Message: 'Order already confirmed',
                                            checkStatus
                                        });
                                    }

                                    // Cập nhật đơn hàng spa
                                    await BookSpaModel.MODEL.updateWhereClause({
                                        orderId
                                    }, {
                                        status: 1
                                    }); // chờ làm dịch vụ

                                    // update status Thành công
                                    await VpnPayModel.MODEL.updateVpnPayByCondition({
                                        userId,
                                        orderId,
                                    }, {
                                        status: 1
                                    });

                                    let vpnPay = await VpnPayModel.MODEL.getVpnPayWhere({
                                        userId,
                                        orderId
                                    });

                                    // Cập nhật ví tổng hệ thống
                                    // TODO: cần kiểm tra lại logic này
                                    if (vpnPay) {
                                        WalletOnlineModel.MODEL.updateWallet({
                                            $inc: {
                                                total: Number(vpnPay.amount)
                                            }
                                        });
                                    }
                                    return ChildRouter.responsePayment(req, res, {
                                        RspCode: '00',
                                        Message: 'Confirm Success'
                                    });


                                } else {
                                    // Thất bại
                                    await VpnPayModel.MODEL.updateVpnPayByCondition({
                                        userId,
                                        orderId,
                                    }, {
                                        status: 2
                                    });
                                    // Cập nhật đơn hàng spa
                                    BookSpaModel.MODEL.updateWhereClause({
                                        orderId
                                    }, {
                                        status: 2
                                    });
                                    // status 2 = huy don hang
                                    return ChildRouter.responsePayment(req, res, {
                                        RspCode: '00',
                                        Message: 'Success'
                                    });
                                }
                            } else {
                                // Chứ ký không hợp lệ
                                return ChildRouter.responsePayment(req, res, {
                                    RspCode: '97',
                                    Message: 'Invalid signature'
                                });
                            }
                        } catch (error) {
                            return ChildRouter.responsePayment(req, res, {
                                RspCode: '99',
                                Message: 'Unknow error'
                            });
                        }
                    }]
                },
            },

            '/parse-file-lich-hen1111.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'all',
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            // lichHen,
                            indexService
                        } = req.body
                        let lichHen = await BookSpaModel.MODEL.getBookSpaByCondition({});
                        console.log('lichHen', lichHen)
                        lichHen.forEach(item => {
                            item.typePetText = item.typePet == 0 ? 'Ô tô' : item.typePet == 1 ? 'Xe máy' : 'Phương tiện khác'
                            item.timeDateIn = TimeUtils.parseTimeFormat4(Number(item.timeCheckIn))
                            item.statusText = item.status == 0 ? 'Chờ duyệt' : item.status == 1 ? 'Chờ làm dịch vụ' : item.status == 2 ? 'Thất bại' : 'Hoàn thành'
                            if (indexService == 2) {
                                item.timeDateOut = TimeUtils.parseTimeFormat4(Number(item.timeCheckOut))
                            }
                        })
                        let objKey = {
                            1: {
                                file: `./files-mau/lich-hen-phong-kham.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'storeName',
                                    'C': 'userName',
                                    'D': 'email',
                                    'E': 'phone',
                                    'F': 'typePetText',
                                    'G': 'timeDateIn',
                                    'H': 'symptom',
                                    'I': 'statusText',
                                },
                                nameFile: 'LichHenPhongKham'
                            },
                            2: {
                                file: `./files-mau/lich-hen-khach-san.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'storeName',
                                    'C': 'userName',
                                    'D': 'email',
                                    'E': 'phone',
                                    'F': 'typePetText',
                                    'G': 'timeDateIn',
                                    'H': 'timeDateOut',
                                    'I': 'note',
                                    'J': 'statusText',
                                },
                                nameFile: 'LichHenKhachSan'
                            },
                            3: {
                                file: `./files-mau/lich-hen-spa.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'storeName',
                                    'C': 'userName',
                                    'D': 'email',
                                    'E': 'phone',
                                    'F': 'typePetText',
                                    'G': 'timeDateIn',
                                    'H': 'service',
                                    'I': 'statusText',
                                },
                                nameFile: 'LichHenSpa'
                            },
                        }
                        let data = XLSX.readFile(objKey[indexService].file, {
                            raw: true,
                            cellStyles: true
                        });
                        let dataKey = objKey[indexService].dataKey
                        let check = false
                        for (let keySheet in data.Sheets) {
                            if (!check) {
                                let styles = {}
                                let keyAplphas = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J']
                                keyAplphas.forEach(item => {
                                    if (data.Sheets[keySheet][`${item}4`]) {
                                        styles[item] = data.Sheets[keySheet][`${item}4`].s
                                    }
                                })
                                check = true;
                                let start = 3
                                lichHen.forEach((schedule, index) => {
                                    schedule.index = index + 1;
                                    start++;
                                    for (let key of keyAplphas) {
                                        if (dataKey[key]) {
                                            if (!data.Sheets[keySheet][`${key}${start}`]) {
                                                data.Sheets[keySheet][`${key}${start}`] = {}
                                            }
                                            data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                            data.Sheets[keySheet][`${key}${start}`].t = 's'
                                            data.Sheets[keySheet][`${key}${start}`].v = schedule[dataKey[key]].toString().trim()
                                            data.Sheets[keySheet][`${key}${start}`].r = `<t>${schedule[dataKey[key]].toString().trim()}</t>`
                                            data.Sheets[keySheet][`${key}${start}`].h = schedule[dataKey[key]].toString().trim()
                                            data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                        } else {
                                            data.Sheets[keySheet][`${key}${start}`] = data.Sheets[keySheet][`${key}4`]
                                        }
                                    }
                                })
                                data.Sheets[keySheet]['!ref'] = `A1:J${start}`
                            }
                        }
                        let path = `/files/${objKey[indexService].nameFile}-${new Date().getTime()}.xlsx`
                        XLSX.writeFile(data, `.${path}`, {
                            bookSST: true,
                            bookType: "xlsx",
                            Props: {
                                Author: "SheetJS"
                            }
                        });
                        setTimeout(() => {
                            FileUtils.deleteFile(APP.BASE_DIR + path);
                        }, 600000);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            path
                        })
                    }]
                },
            },
            '/parse-file-lich-hen.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'all',
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            indexService
                        } = req.body

                        let fileName = '';
                        let sheetName = '';
                        let services = await ServicesModel.MODEL.getServicesByCondition({})
                        let listService = {}
                        services.forEach(item => {
                            listService[item._id] = item
                        })
                        let lichHen = null;

                        switch (Number(indexService)) {
                            case 1:
                                lichHen = await BookExaminationModel.MODEL.getBookExaminationByConditionDetail({}, {createAt: -1})
                                fileName = 'LichHenPhongKham';
                                sheetName = 'Lịch hẹn gara';
                                break;
                            case 2:
                                lichHen = await BookRoomModel.MODEL.getBookRoomByConditionDetail({}, {createAt: -1})
                                fileName = 'LichHenPhongKhachSan';
                                sheetName = 'Lịch hẹn điểm gửi xe';
                                break;
                            case 3:
                                lichHen = await BookSpaModel.MODEL.getBookSpaByConditionDetail({}, {createAt: -1})
                                fileName = 'LichHenSpa';
                                sheetName = 'Lịch hẹn Spa';
                                break;
                            default:
                            // code block
                        }

                        if (lichHen) {

                            lichHen.forEach(item => {
                                item.storeName = listService[item.storeId] ? listService[item.storeId].name : ''
                                item.timeDateIn = TimeUtils.parseTimeFormat4(Number(item.timeCheckIn))
                                item.statusText = item.status == 0 ? 'Chờ duyệt' : item.status == 1 ? 'Chờ làm dịch vụ' : item.status == 2 ? 'Thất bại' : 'Hoàn thành'
                                item.isPayOnline = Number(item.isPayOnline) === 0 ? 'Chưa thanh toán' : Number(item.isPayOnline) === 1 ? 'Đã thanh toán' : 'Không rõ'
                                item.createAt = TimeUtils.parseTimeFormat4(Number(item.createAt))
                            })

                            const wb = new xl.Workbook();
                            const ws = wb.addWorksheet(sheetName);

                            const headingColumnNames = [
                                {
                                    name: "Mã đơn",
                                    key: "orderId",
                                },
                                {
                                    name: "Thương hiệu",
                                    key: "storeName",
                                },
                                {
                                    name: "Người đặt",
                                    key: "userName",
                                },
                                {
                                    name: "Số điện thoại",
                                    key: "phone",
                                },
                                {
                                    name: "Thời gian đặt",
                                    key: "createAt",
                                },
                                {
                                    name: "Thời gian làm DV",
                                    key: "timeDateIn",
                                },
                                {
                                    name: "Dịch vụ",
                                    key: "service",
                                },
                                {
                                    name: "Giá tiền",
                                    key: "price",
                                },
                                {
                                    name: "Mã giảm giá",
                                    key: "coupon",
                                },
                                {
                                    name: "Thanh toán",
                                    key: "isPayOnline",
                                },
                                {
                                    name: "Tình trạng",
                                    key: "statusText",
                                },
                            ]
                            let headingColumnIndex = 1;
                            headingColumnNames.forEach(heading => {
                                ws.cell(1, headingColumnIndex++).string(heading.name)
                            });
                            let rowIndex = 2;
                            lichHen.forEach(record => {
                                let columnIndex = 1;
                                headingColumnNames.forEach(heading => {

                                    ws.cell(rowIndex, columnIndex++).string(String(!record[heading.key] ? '' : record[heading.key]))
                                });
                                rowIndex++;
                            });

                            let path = `files/${fileName}-${new Date().getTime()}.xlsx`

                            wb.write(path, function (err, stats) {
                                if (err) {
                                    console.error(err);
                                } else {
                                    path = '/' + path;
                                    return ChildRouter.responseSuccess("Thành công", res, {
                                        path
                                    })
                                }
                            });
                        } else {
                            return ChildRouter.responseError('Có lỗi', res, {})
                        }

                    }]
                },
            },

            '/parse-file-don-hang.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'all',
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            bills,
                            indexService
                        } = req.body
                        bills.forEach(bill => {
                            bill.paymentsText = (bill.payments == 0 ? 'COD' : bill.payments == 1 ? 'Chuyển khoản' : bill.payments == 2 ? 'Tại quầy' : 'Online') +
                                (bill.payments == 3 ? bill.isPayOnline == 1 ? '-Đã chuyển' : '-Chưa chuyển' : '')
                            bill.timeDateIn = TimeUtils.parseTimeFormat4(Number(bill.createAt))
                            bill.codeText = `${bill.code}`
                        })
                        let objKey = {
                            1: {
                                file: `./files-mau/danh-sach-don-hang.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'codeText',
                                    'C': 'listProduct',
                                    'D': 'userName',
                                    'E': 'timeDateIn',
                                    'F': 'totalMoney',
                                    'G': 'paymentsText',
                                    'H': 'statusText',
                                },
                                nameFile: 'DanhSachDonHang'
                            },
                        }
                        let data = XLSX.readFile(objKey[indexService].file, {
                            raw: true,
                            cellStyles: true
                        });
                        let dataKey = objKey[indexService].dataKey
                        let check = false
                        for (let keySheet in data.Sheets) {
                            if (!check) {
                                let styles = {}
                                let keyAplphas = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
                                keyAplphas.forEach(item => {
                                    if (data.Sheets[keySheet][`${item}4`]) {
                                        styles[item] = data.Sheets[keySheet][`${item}4`].s
                                    }
                                })
                                check = true;
                                let start = 3
                                bills.forEach((schedule, index) => {
                                    schedule.index = index + 1;
                                    start++;
                                    for (let key of keyAplphas) {
                                        if (dataKey[key]) {
                                            if (!data.Sheets[keySheet][`${key}${start}`]) {
                                                data.Sheets[keySheet][`${key}${start}`] = {}
                                            }
                                            if (dataKey[key] == 'index' || dataKey[key] == 'totalMoney') {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 'n'
                                                data.Sheets[keySheet][`${key}${start}`].v = Number(schedule[dataKey[key]])
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            } else {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 's'
                                                data.Sheets[keySheet][`${key}${start}`].v = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].r = `<t>${schedule[dataKey[key]].toString().trim()}</t>`
                                                data.Sheets[keySheet][`${key}${start}`].h = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            }
                                        } else {
                                            data.Sheets[keySheet][`${key}${start}`] = data.Sheets[keySheet][`${key}4`]
                                        }
                                    }
                                })
                                data.Sheets[keySheet]['!ref'] = `A1:H${start}`
                            }
                        }
                        let path = `/files/${objKey[indexService].nameFile}-${new Date().getTime()}.xlsx`
                        XLSX.writeFile(data, `.${path}`, {
                            bookSST: true,
                            bookType: "xlsx",
                            Props: {
                                Author: "SheetJS"
                            }
                        });
                        setTimeout(() => {
                            FileUtils.deleteFile(APP.BASE_DIR + path);
                        }, 600000);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            path
                        })
                    }]
                },
            },

            '/parse-file-nguoi-kinh-doanh.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'all',
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            users,
                            indexTable
                        } = req.body
                        users.forEach(user => {
                            user.timeDateIn = TimeUtils.parseTimeFormat4(Number(user.createAt))
                            if (indexTable == 1) {
                                let currentTime = new Date().getTime() - user.createAt;
                                let days = Number(currentTime / 1000 / 60 / 60 / 24).toFixed(1);
                                user.timeActive = `${days} ngày`
                            } else {
                                if (user.fee == 1 && user.statusStore == 1 && user.status == 1) {
                                    user.statusText = 'Đã tính phí'
                                }
                                if (user.statusStore == 0 && user.status == 1) {
                                    user.statusText = 'Đang chờ duyệt'
                                }
                                if (user.statusStore == 2 && user.status == 1) {
                                    user.statusText = 'Duyệt thất bại'
                                }
                                if (user.status == 2) {
                                    user.statusText = 'Đang bị khóa'
                                }
                            }
                        })
                        let objKey = {
                            1: {
                                file: `./files-mau/danh-sach-nguoi-kinh-doanh-chua-tinh-phi.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'fullName',
                                    'C': 'email',
                                    'D': 'phone',
                                    'E': 'address',
                                    'F': 'timeDateIn',
                                    'G': 'timeActive',
                                    'H': 'doanhThu',
                                },
                                nameFile: 'DanhSachKinhDoanh'
                            },
                            2: {
                                file: `./files-mau/danh-sach-nguoi-kinh-doanh.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'fullName',
                                    'C': 'email',
                                    'D': 'phone',
                                    'E': 'address',
                                    'F': 'timeDateIn',
                                    'G': 'statusText',
                                },
                                nameFile: 'DanhSachKinhDoanh'
                            },
                        }
                        let data = XLSX.readFile(objKey[indexTable].file, {
                            raw: true,
                            cellStyles: true
                        });
                        let dataKey = objKey[indexTable].dataKey
                        let check = false
                        for (let keySheet in data.Sheets) {
                            if (!check) {
                                let styles = {}
                                let keyAplphas = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
                                keyAplphas.forEach(item => {
                                    if (data.Sheets[keySheet][`${item}4`]) {
                                        styles[item] = data.Sheets[keySheet][`${item}4`].s
                                    }
                                })
                                check = true;
                                let start = 3
                                users.forEach((schedule, index) => {
                                    schedule.index = index + 1;
                                    start++;
                                    for (let key of keyAplphas) {
                                        if (dataKey[key]) {
                                            if (!data.Sheets[keySheet][`${key}${start}`]) {
                                                data.Sheets[keySheet][`${key}${start}`] = {}
                                            }
                                            if (dataKey[key] == 'index' || dataKey[key] == 'doanhThu') {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 'n'
                                                data.Sheets[keySheet][`${key}${start}`].v = Number(schedule[dataKey[key]])
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            } else {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 's'
                                                data.Sheets[keySheet][`${key}${start}`].v = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].r = `<t>${schedule[dataKey[key]].toString().trim()}</t>`
                                                data.Sheets[keySheet][`${key}${start}`].h = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            }
                                        } else {
                                            data.Sheets[keySheet][`${key}${start}`] = data.Sheets[keySheet][`${key}4`]
                                        }
                                    }
                                })
                                data.Sheets[keySheet]['!ref'] = `A1:H${start}`
                            }
                        }
                        let path = `/files/${objKey[indexTable].nameFile}-${new Date().getTime()}.xlsx`
                        XLSX.writeFile(data, `.${path}`, {
                            bookSST: true,
                            bookType: "xlsx",
                            Props: {
                                Author: "SheetJS"
                            }
                        });
                        setTimeout(() => {
                            FileUtils.deleteFile(APP.BASE_DIR + path);
                        }, 600000);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            path
                        })
                    }]
                },
            },

            '/parse-file-shop-trung-tam.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'all',
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            services,
                            indexTable
                        } = req.body
                        services.forEach(service => {
                            service.codeMP = `${indexTable == 1 ? 'CH' : indexTable == 2 ? 'PK' : indexTable == 3 ? 'KS' : 'SP'}${service.code}`
                            service.typePetText = service.typePet == 0 ? 'Ô tô' : service.typePet == 1 ? 'Xe máy' : service.typePet == 2 ? 'Phương tiện khác' : 'Tất cả'
                            service.statusText = service.status == 0 ? 'Ẩn' : 'Hiển thị'
                        })
                        let textTitle = indexTable == 1 ? 'Cửa hàng' : indexTable == 2 ? 'Xưởng dịch vụ' : indexTable == 3 ? 'Điểm gửi xe' : 'Spa'
                        indexTable = 1
                        let objKey = {
                            1: {
                                file: `./files-mau/danh-sach-shop-trung-tam.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'codeMP',
                                    'C': 'name',
                                    'D': 'address',
                                    'E': 'typePetText',
                                    'F': 'statusText',
                                },
                                nameFile: 'DanhSachKinhDoanh'
                            },
                        }
                        let data = XLSX.readFile(objKey[indexTable].file, {
                            raw: true,
                            cellStyles: true
                        });
                        let dataKey = objKey[indexTable].dataKey
                        let check = false
                        for (let keySheet in data.Sheets) {
                            if (!check) {
                                let styles = {}
                                let keyAplphas = ['A', 'B', 'C', 'D', 'E', 'F']
                                keyAplphas.forEach(item => {
                                    if (data.Sheets[keySheet][`${item}4`]) {
                                        styles[item] = data.Sheets[keySheet][`${item}4`].s
                                    }
                                })
                                data.Sheets[keySheet]['B1'].v = `Danh sách ${textTitle}`
                                data.Sheets[keySheet]['B1'].w = `Danh sách ${textTitle}`
                                data.Sheets[keySheet]['B1'].h = `Danh sách ${textTitle}`
                                data.Sheets[keySheet]['B1'].r = `<t>Danh sách ${textTitle}</t>`
                                data.Sheets[keySheet]['B3'].v = `Mã ${textTitle}`
                                data.Sheets[keySheet]['B3'].w = `Mã ${textTitle}`
                                data.Sheets[keySheet]['B3'].h = `Mã ${textTitle}`
                                data.Sheets[keySheet]['B3'].r = `<t>Mã ${textTitle}</t>`
                                data.Sheets[keySheet]['C3'].v = `Tên ${textTitle}`
                                data.Sheets[keySheet]['C3'].w = `Tên ${textTitle}`
                                data.Sheets[keySheet]['C3'].h = `Tên ${textTitle}`
                                data.Sheets[keySheet]['C3'].r = `<t>Tên ${textTitle}</t>`
                                check = true;
                                let start = 3
                                services.forEach((schedule, index) => {
                                    schedule.index = index + 1;
                                    start++;
                                    for (let key of keyAplphas) {
                                        if (dataKey[key]) {
                                            if (!data.Sheets[keySheet][`${key}${start}`]) {
                                                data.Sheets[keySheet][`${key}${start}`] = {}
                                            }
                                            if (dataKey[key] == 'index' || dataKey[key] == 'doanhThu') {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 'n'
                                                data.Sheets[keySheet][`${key}${start}`].v = Number(schedule[dataKey[key]])
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            } else {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 's'
                                                data.Sheets[keySheet][`${key}${start}`].v = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].r = `<t>${schedule[dataKey[key]].toString().trim()}</t>`
                                                data.Sheets[keySheet][`${key}${start}`].h = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            }
                                        } else {
                                            data.Sheets[keySheet][`${key}${start}`] = data.Sheets[keySheet][`${key}4`]
                                        }
                                    }
                                })
                                data.Sheets[keySheet]['!ref'] = `A1:F${start}`
                            }
                        }
                        let path = `/files/${objKey[indexTable].nameFile}-${new Date().getTime()}.xlsx`
                        XLSX.writeFile(data, `.${path}`, {
                            bookSST: true,
                            bookType: "xlsx",
                            Props: {
                                Author: "SheetJS"
                            }
                        });
                        setTimeout(() => {
                            FileUtils.deleteFile(APP.BASE_DIR + path);
                        }, 600000);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            path
                        })
                    }]
                },
            },

            '/parse-file-don-hang-tra-hang.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'all',
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            bills
                        } = req.body
                        let indexService = 1
                        bills.forEach(bill => {
                            bill.statusText = (!bill.adminConfirm || bill.adminConfirm == 0) ? 'Chờ xử lý' : 'Đã xác nhận'
                            bill.timeDateIn = TimeUtils.parseTimeFormat4(Number(bill.createAt))
                            bill.codeText = `${bill.code}`
                        })
                        let objKey = {
                            1: {
                                file: `./files-mau/danh-sach-don-hang-tra-hang.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'codeText',
                                    'C': 'listProduct',
                                    'D': 'storeName',
                                    'E': 'userName',
                                    'F': 'timeDateIn',
                                    'G': 'totalMoney',
                                    'H': 'statusText',
                                },
                                nameFile: 'DanhSachDonHang'
                            },
                        }
                        let data = XLSX.readFile(objKey[indexService].file, {
                            raw: true,
                            cellStyles: true
                        });
                        let dataKey = objKey[indexService].dataKey
                        let check = false
                        for (let keySheet in data.Sheets) {
                            if (!check) {
                                let styles = {}
                                let keyAplphas = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
                                keyAplphas.forEach(item => {
                                    if (data.Sheets[keySheet][`${item}4`]) {
                                        styles[item] = data.Sheets[keySheet][`${item}4`].s
                                    }
                                })
                                check = true;
                                let start = 3
                                bills.forEach((schedule, index) => {
                                    schedule.index = index + 1;
                                    start++;
                                    for (let key of keyAplphas) {
                                        if (dataKey[key]) {
                                            if (!data.Sheets[keySheet][`${key}${start}`]) {
                                                data.Sheets[keySheet][`${key}${start}`] = {}
                                            }
                                            if (dataKey[key] == 'index' || dataKey[key] == 'totalMoney') {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 'n'
                                                data.Sheets[keySheet][`${key}${start}`].v = Number(schedule[dataKey[key]])
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            } else {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 's'
                                                data.Sheets[keySheet][`${key}${start}`].v = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].r = `<t>${schedule[dataKey[key]].toString().trim()}</t>`
                                                data.Sheets[keySheet][`${key}${start}`].h = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            }
                                        } else {
                                            data.Sheets[keySheet][`${key}${start}`] = data.Sheets[keySheet][`${key}4`]
                                        }
                                    }
                                })
                                data.Sheets[keySheet]['!ref'] = `A1:H${start}`
                            }
                        }
                        let path = `/files/${objKey[indexService].nameFile}-${new Date().getTime()}.xlsx`
                        XLSX.writeFile(data, `.${path}`, {
                            bookSST: true,
                            bookType: "xlsx",
                            Props: {
                                Author: "SheetJS"
                            }
                        });
                        setTimeout(() => {
                            FileUtils.deleteFile(APP.BASE_DIR + path);
                        }, 600000);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            path
                        })
                    }]
                },
            },

            '/parse-file-san-pham.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'all',
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            products,
                            indexTable
                        } = req.body
                        products.forEach(product => {
                            product.statusText = product.typeProduct == 1 ? 'Còn hàng' : 'Hết hàng'
                            product.timeDateIn = TimeUtils.parseTimeFormat4(Number(product.createAt))
                            product.code = 'SP' + product.code
                        })
                        let textTitle = indexTable == 1 ? 'Đang hiển thị' : indexTable == 2 ? 'Bị ẩn' : 'Chờ duyệt'
                        indexTable = 1
                        let objKey = {
                            1: {
                                file: `./files-mau/danh-sach-san-pham.xlsx`,
                                dataKey: {
                                    'A': 'index',
                                    'B': 'code',
                                    'C': 'name',
                                    'D': 'storeName',
                                    'E': 'categoryName',
                                    'F': 'price',
                                    'G': 'statusText',
                                    'H': 'timeDateIn',
                                },
                                nameFile: 'DanhSachSanPham'
                            },
                        }
                        let data = XLSX.readFile(objKey[indexTable].file, {
                            raw: true,
                            cellStyles: true
                        });
                        let dataKey = objKey[indexTable].dataKey
                        let check = false
                        for (let keySheet in data.Sheets) {
                            if (!check) {
                                let styles = {}
                                let keyAplphas = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
                                keyAplphas.forEach(item => {
                                    if (data.Sheets[keySheet][`${item}4`]) {
                                        styles[item] = data.Sheets[keySheet][`${item}4`].s
                                    }
                                })
                                data.Sheets[keySheet]['B1'].v = `Danh sách sản phẩm ${textTitle}`
                                data.Sheets[keySheet]['B1'].w = `Danh sách sản phẩm ${textTitle}`
                                data.Sheets[keySheet]['B1'].h = `Danh sách sản phẩm ${textTitle}`
                                data.Sheets[keySheet]['B1'].r = `<t>Danh sách sản phẩm ${textTitle}</t>`
                                check = true;
                                let start = 3
                                products.forEach((schedule, index) => {
                                    schedule.index = index + 1;
                                    start++;
                                    for (let key of keyAplphas) {
                                        if (dataKey[key]) {
                                            if (!data.Sheets[keySheet][`${key}${start}`]) {
                                                data.Sheets[keySheet][`${key}${start}`] = {}
                                            }
                                            if (dataKey[key] == 'index' || dataKey[key] == 'price') {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 'n'
                                                data.Sheets[keySheet][`${key}${start}`].v = Number(schedule[dataKey[key]])
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            } else {
                                                data.Sheets[keySheet][`${key}${start}`].s = styles[key]
                                                data.Sheets[keySheet][`${key}${start}`].t = 's'
                                                data.Sheets[keySheet][`${key}${start}`].v = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].r = `<t>${schedule[dataKey[key]].toString().trim()}</t>`
                                                data.Sheets[keySheet][`${key}${start}`].h = schedule[dataKey[key]].toString().trim()
                                                data.Sheets[keySheet][`${key}${start}`].w = schedule[dataKey[key]].toString().trim()
                                            }
                                        } else {
                                            data.Sheets[keySheet][`${key}${start}`] = data.Sheets[keySheet][`${key}4`]
                                        }
                                    }
                                })
                                data.Sheets[keySheet]['!ref'] = `A1:H${start}`
                            }
                        }
                        let path = `/files/${objKey[indexTable].nameFile}-${new Date().getTime()}.xlsx`
                        XLSX.writeFile(data, `.${path}`, {
                            bookSST: true,
                            bookType: "xlsx",
                            Props: {
                                Author: "SheetJS"
                            }
                        });
                        setTimeout(() => {
                            FileUtils.deleteFile(APP.BASE_DIR + path);
                        }, 600000);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            path
                        })
                    }]
                },
            },


            '/resend-email.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {
                            email
                        } = req.query;
                        let user = await UserModel.MODEL.getDataWhere({
                            email
                        }, UserModel.MODEL.FIND_ONE());
                        if (!user) {
                            return ChildRouter.responseError("Không có tài khoản nào khớp với email này", res);
                        }
                        if (user.confirmEmail == 1) return ChildRouter.responseError("Tài khoản này đã được xác nhận email. Bạn có thể quay lại màn hình đăng nhập", res);

                        let code = await CodeEmailModel.MODEL.randomCodeEmail(email, 50);
                        MailUser.sendEmailConfirmAccount(email, '/xac-nhan-tai-khoan-email.html?email=' + email + '&code=' + code);
                        return ChildRouter.responseSuccess("Gửi lại mail thành công", res);
                    }],
                },
            },

            '/update-data.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const services = await ServicesModel.MODEL.getServicesByCondition({});
                        services.forEach(x => {
                            if (x.province) {
                                x.provinceUTF = StringUtils.removeUtf8(x.province);
                                ServicesModel.MODEL.coll.update({
                                        _id: ObjectId(x._id)
                                    }, x, {
                                        multi: true,
                                        runValidators: true
                                    },
                                    function (error) {
                                        console.log(error);
                                    });
                            }
                        });
                        return ChildRouter.response(res, {
                            services
                        });
                        // Shell update long, lat in branches
                        // db.branches.find({
                        //     lat: {
                        //         $exists: true,
                        //         $ne: ""
                        //     },
                        //     lng: {
                        //         $exists: true,
                        //         $ne: ""
                        //     }
                        // }).forEach(function (doc) {
                        //     db.branches.update({
                        //         _id: doc._id
                        //     }, {
                        //         $set: {
                        //             location: {
                        //                 type: "Point",
                        //                 coordinates: [parseFloat(doc.lng), parseFloat(doc.lat)]
                        //             }
                        //         }
                        //     });
                        // })
                        // db.branches.createIndex({location:"2dsphere"});
                    }],
                },
            },

            '/search-by-neighborhood.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const {
                            longitude,
                            latitude,
                            maxDistance
                        } = req.query;
                        // const storesId = await BranchModel.MODEL.getDataWhereAndSelectField({
                        //     location: {
                        //         $near: {
                        //             $geometry: {
                        //                 type: 'Point',
                        //                 coordinates: [longitude, latitude]
                        //             },
                        //             $maxDistance: maxDistance || 5000,
                        //         }
                        //     }
                        // }, 'storeId', BranchModel.MODEL.FIND_MANY)
                        const branches = await BranchModel.MODEL.getBranchByCondition({
                            location: {
                                $near: {
                                    $geometry: {
                                        type: 'Point',
                                        coordinates: [longitude, latitude]
                                    },
                                    $maxDistance: maxDistance || 5000,
                                }
                            }
                        })
                        const storesId = branches.map(x => x.storeId).filter((value, index, arr) => arr.findIndex(x => x === value) === index);
                        const services = await ServicesModel.MODEL.getServicesByCondition({
                            _id: {
                                $in: storesId
                            }
                        });
                        // Cách này chưa tối ưu bằng query join vì làm gấp :D
                        return ChildRouter.response(res, {
                            services
                        });
                    }],
                },
            },

            // '/update-data-hotel.html': {
            //     config: {
            //         auth: [this.roles.all],
            //         get: 'json',
            //     },
            //
            //     methods: {
            //         get: [async function (req,res) {
            //             const hotels = await ServicesModel.MODEL.getServicesByCondition({type: 2, status: 1});
            //
            //             hotels.forEach(x => {
            //
            //                 if (x.khoangGia) {
            //
            //                     // let khoangGia = JSON.parse(x.khoangGia);
            //                     let khoangGia = x.khoangGia;
            //                     let giaDog = khoangGia.dog;
            //                     let giaCat = khoangGia.cat;
            //                     if (giaDog) {
            //                         for (const item of giaDog) {
            //                             const newDataClassify = {data: item.data,name: "Phòng cho cún"}
            //                             let price = item.data[0].price.replace(/[^0-9\.]+/g,"");
            //
            //                             let svNewService = {
            //                                 userId: x.userId,
            //                                 name: item.name,
            //                                 nameUTF: StringUtils.removeUtf8(item.name),
            //                                 descriptionUTF: StringUtils.removeUtf8(item.name),
            //                                 storeId: x._id,
            //                                 branchId: '',
            //                                 categoryId: '',
            //                                 description: item.name,
            //                                 price: price,
            //                                 thumbail: '',
            //                                 typeService: 1,
            //                                 classify: newDataClassify,
            //                                 shortDes: item.name,
            //                             }
            //                             RoomOfHotelModel.MODEL.addRoom(svNewService);
            //                         }
            //                     }
            //                     if (giaCat) {
            //                         for (const item of giaCat) {
            //                             const newDataClassify = {data: item.data,name: "Phòng Cho Mèo"}
            //                             let price = item.data[0].price.replace(/[^0-9\.]+/g,"");
            //
            //                             let svNewService = {
            //                                 userId: x.userId,
            //                                 name: item.name,
            //                                 nameUTF: StringUtils.removeUtf8(item.name),
            //                                 descriptionUTF: StringUtils.removeUtf8(item.name),
            //                                 storeId: x._id,
            //                                 branchId: '',
            //                                 categoryId: '',
            //                                 description: item.name,
            //                                 price: price,
            //                                 thumbail: '',
            //                                 typeService: 1,
            //                                 classify: newDataClassify,
            //                                 shortDes: item.name,
            //                             }
            //                             RoomOfHotelModel.MODEL.addRoom(svNewService);
            //                         }
            //                         // obj.khoangGia = {};
            //                     }
            //                 }
            //             });
            //             return ChildRouter.response(res,{
            //                 hotels
            //             });
            //         }],
            //     },
            // },

            // Thêm tên dịch vụ của brand vào mảng tags của brand phục vụ việc search.
            // '/update-data-tags.html': {
            //     config: {
            //         auth: [this.roles.all],
            //         get: 'json',
            //     },
            //
            //     methods: {
            //         get: [async function (req,res) {
            //             const services = await ServicesModel.MODEL.getServicesByCondition({});
            //             for (const x of services) {
            //                 const tags = []; // remove data tag cũ
            //                 let spa = await ServiceOfSpaModel.MODEL.getServicePageByCondition({storeId: x._id.toString(),status:1}, 1, 1000);
            //
            //                 if (spa){
            //                     spa.forEach(item => {
            //                        const name = !item.name ? '' : StringUtils.removeUtf8(item.name);
            //                        if(name){
            //                            tags.push(name);
            //                            if (tags){
            //                                x.tags = tags;
            //                            }
            //                        }
            //                        const rs = ServicesModel.MODEL.updateServices(x._id, x);
            //                     });
            //                 }
            //             }
            //             return ChildRouter.response(res,{
            //                 services
            //             });
            //         }],
            //     },
            // },

            // Update businessTypes
            '/update-data-business-type.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const migrated = true;

                        if (migrated) {
                            return ChildRouter.response(res, {
                                msg: "Đã hoàn tất. Chạy lại vui lòng chuyển Migrated = false!!!"
                            });
                        }

                        const services = await ServicesModel.MODEL.getServicesByCondition({});
                        for (const x of services) {
                            const oldType = x.type;
                            if (oldType) {
                                let businessTypes = [];
                                businessTypes.push(oldType);
                                businessTypes = _.uniq(businessTypes); // Delete duplicate
                                // Update to Brand
                                await ServicesModel.MODEL.updateWhereClause({
                                    _id: x._id.toString()
                                }, {businessTypes: businessTypes});
                            }

                        }
                        return ChildRouter.response(res, {
                            services
                        });
                    }],
                },
            },

            '/update-data-location-for-branch': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        const migrated = true;

                        if (migrated) {
                            return ChildRouter.response(res, {
                                msg: "Đã hoàn tất. Chạy lại vui lòng chuyển Migrated = false!!!"
                            });
                        }
                        let listBranch = await BranchModel.MODEL.getBranchByCondition({status: 1})

                        for (const branch of listBranch) {
                            console.log('listBranch', branch._id.toString())
                            const data = await MapUtil.updateBranchLonLat(branch._id);
                        }

                        // const data = await MapUtil.updateBranchLonLat('5ed26eb7cd3983136747e2dd');

                        return ChildRouter.responseSuccess("Thanh Cong", res, {});
                    }],
                },
            },
            '/update-report-old-data': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let migrated = true;
                        if (migrated) {
                            return ChildRouter.responseSuccess("Vui long cau hinh de thuc hien", res);
                        }
                        let bookSpas = await BookSpaModel.MODEL.getBookSpaByCondition({
                            status: 3
                        })
                        for (const order of bookSpas) {
                            await ReportUtil.updateReportByOrderId(order._id.toString(), TypeServices.SPA)
                        }
                        return ChildRouter.responseSuccess("Thanh Cong", res, {});
                    }],
                },
            },

            // '/update-data-hotel.html': {
            //     config: {
            //         auth: [this.roles.all],
            //         get: 'json',
            //     },
            //
            //     methods: {
            //         get: [async function (req,res) {
            //             const rooms = await RoomOfHotelModel.MODEL.getAllRooms({});
            //             for (const x of rooms) {
            //                 console.log(x.userId)
            //                 const service = await ServicesModel.MODEL.getServicesByCondition({userId: x.userId, type: 3});
            //                 console.log(service)
            //                 if (service && service.length) {
            //                     const primaryServiceId = service[0]._id.toString()
            //                     x.storeId = primaryServiceId
            //                     console.log('data old: ', x)
            //                     const rs = await RoomOfHotelModel.MODEL.updateRoom(x._id, x)
            //                     console.log(rs)
            //                     const dataNew = await RoomOfHotelModel.MODEL.getRoomById(x._id)
            //                     console.log('data old: ', dataNew)
            //                 }
            //             }
            //             return ChildRouter.response(res,{
            //                 rooms
            //             });
            //         }],
            //     },
            // },
            '/update-province-data': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let migrated = true;
                        if (migrated) {
                            return ChildRouter.responseSuccess("Vui long cau hinh de thuc hien", res);
                        }
                        const rs = await MapUtil.updateProvinceLngLat()
                        return ChildRouter.responseSuccess("Thanh Cong", res, rs);
                    }],
                },
            },
            '/updateShipment-ghtk': {
                config: {
                    auth: [this.roles.all],
                    post: 'json'
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            label_id,
                            partner_id, // orderId
                            action_time,
                            status_id,
                            reason_code,
                            reason,
                            weight,
                            fee,
                            return_part_package,
                        } = req.body;


                        let {hash} = req.query;

                        try {
                            let db = firebaseAdmin.database()
                            // const docRef = db.collection('paymentVnPay').doc(id).collection('data');
                            let docRef = db.ref(`ghtk/updateShipment/${label_id}`);
                            docRef.set({body: JSON.stringify(req.body), query: JSON.stringify(req.query)});

                        } catch (e) {
                            console.log(e)
                        }

                        // 1	Hủy đơn hàng
                        // 1	Chưa tiếp nhận
                        // 2	Đã tiếp nhận
                        // 3	Đã lấy hàng/Đã nhập kho
                        // 4	Đã điều phối giao hàng/Đang giao hàng
                        // 5	Đã giao hàng/Chưa đối soát
                        // 6	Đã đối soát
                        // 7	Không lấy được hàng
                        // 8	Hoãn lấy hàng
                        // 9	Không giao được hàng
                        // 10	Delay giao hàng
                        // 11	Đã đối soát công nợ trả hàng
                        // 12	Đã điều phối lấy hàng/Đang lấy hàng
                        // 13	Đơn hàng bồi hoàn
                        // 20	Đang trả hàng (COD cầm hàng đi trả)
                        // 21	Đã trả hàng (COD đã trả xong hàng)
                        // 123	Shipper báo đã lấy hàng
                        // 127	Shipper (nhân viên lấy/giao hàng) báo không lấy được hàng
                        // 128	Shipper báo delay lấy hàng
                        // 45	Shipper báo đã giao hàng
                        // 49	Shipper báo không giao được giao hàng
                        // 410	Shipper báo delay giao hàng

                        if (hash != 'maxQ@123') {
                            res.statusCode = 403;
                            return ChildRouter.responseError("Cheating", res);
                        }

                        if (status_id == 45)
                        {
                            let orderId = partner_id.toString().split('-')[1]

                            const order = await RequestBuyProductModel.MODEL.getOneRequestByConditionNoData({
                                orderId: orderId
                            });

                            if (order && order.status !== ProductOrderStatus.COMPLETED) {
                                let obj = {
                                    label_id,
                                    partner_id,
                                    action_time,
                                    status_id,
                                    reason_code,
                                    reason,
                                    weight,
                                    fee,
                                    return_part_package
                                }
                                const history = ShippingModel.MODEL.add(obj);

                                await RequestBuyProductModel.MODEL.updateWhereClause({
                                    orderId: orderId
                                }, {status: 3});

                                // Cập nhật tích điểm vào bảng Book tương ứng.
                                if (order.totalPriceShop > 0) {
                                    const pointInsert = await PointUtil.MoneyToPoint(order.totalPriceShop);
                                    PointUtil.UpdatePointOrder(
                                        orderId,
                                        pointInsert,
                                        0
                                    ); // Cập nhật vào bảng book
                                    await PointUtil.UpdateUserPoint(order.userId, orderId, pointInsert, 0 , `Bạn được cộng điểm từ đơn hàng ${order.orderId}`); // Cập nhật User Point
                                    // TODO: gửi thông báo tích điểm
                                }
                                // End

                                return ChildRouter.responseSuccess("Update Shipping", res, history);
                            } else {
                                return ChildRouter.responseError("Update Shipping False", res);
                            }
                        } else {
                            return ChildRouter.responseError("Skip Update Status", res);
                        }
                    }]
                },
            },

            '/migrate-services-data': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const migrated = true;

                        if (migrated) {
                            return ChildRouter.response(res, {
                                msg: "Đã hoàn tất. Chạy lại vui lòng chuyển Migrated = false"
                            });
                        }

                        const services = await ServicesModel.MODEL.getServicesByCondition({});
                        let count = 0;
                        for (const x of services) {
                            const district = x.district || '';
                            ServicesModel.MODEL.updateWhereClause({
                                _id: x._id.toString()
                            }, {districtUTF: StringUtils.removeUtf8(district)});
                            count++;
                        }
                        return ChildRouter.response(res, {
                            count
                        });
                    }],
                },
            },

            '/load-product-filter': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let data = require('../../product-filter.json')
                        return ChildRouter.response(res, data);
                    }],
                },
            },

            '/update-data-search': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        ServiceUpdateData.updateDataServiceSearch()
                        return ChildRouter.responseSuccess('Thanh cong', res, {});
                    }],
                },
            },
            '/test-notification-by-notifyId/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await Notification.sendNotification(req.params.id); // push firebase
                        return ChildRouter.responseSuccess('Thanh cong', res, {});
                    }],
                },
            },
            '/report': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        const data = {
                            "BC": [
                                {
                                    "No": "TT",
                                    "content": "NỘI DUNG",
                                    "value": "SỐ LIỆU",
                                    "unit": "ĐVT"
                                },
                                {
                                    "No": 1,
                                    "content": "Số lượng truy cập ",
                                    "value": 46500,
                                    "unit": "Lượt"
                                },
                                {
                                    "No": 2,
                                    "content": "Số người bán "
                                },
                                {
                                    "No": "2.1",
                                    "content": "Tổng số trên hệ thống",
                                    "value": 2,
                                    "unit": "Gian hàng"
                                },
                                {
                                    "No": "2.2",
                                    "content": "Số lượng người bán mới",
                                    "value": 2,
                                    "unit": "Gian hàng"
                                },
                                {
                                    "No": 3,
                                    "content": "Số lượng sản phẩm"
                                },
                                {
                                    "No": "3.1",
                                    "content": "Tổng số sản phẩm (SKU) ",
                                    "value": 36,
                                    "unit": "Sản phẩm"
                                },
                                {
                                    "No": "3.2",
                                    "content": "Số sản phẩm đăng bán mới ",
                                    "value": 36,
                                    "unit": "Sản phẩm"
                                },
                                {
                                    "No": 4,
                                    "content": "Số lượng giao dịch "
                                },
                                {
                                    "No": "4.1",
                                    "content": "Tổng số đơn hàng ",
                                    "value": 2,
                                    "unit": "Đơn hàng"
                                },
                                {
                                    "No": "4.2",
                                    "content": "Tổng số đơn hàng thành công ",
                                    "value": 2,
                                    "unit": "Đơn hàng"
                                },
                                {
                                    "No": "4.3",
                                    "content": "Tổng số đơn hàng không thành công ",
                                    "value": 1,
                                    "unit": "Đơn hàng"
                                },
                                {
                                    "No": "4.4",
                                    "content": "Tổng giá trị giao dịch ",
                                    "unit": "Đơn hàng"
                                }
                            ]
                        }
                        return ChildRouter.responseSuccess('Success', res, data);
                    }],
                },
            },
            '/pvi_callback': {
                config: {
                    auth: [this.roles.all],
                    post: 'json'
                },

                methods: {
                    post: [async function (req, res) {
                        try {
                            let {
                                CardId,
                                SerialNumber,
                                RequestId,
                                PolicyNumber,
                                URL,
                                CpId,
                                Sign,
                            } = req.body;

                            return ChildRouter.responseBHCallBack(req, res, {...req.body});
                        } catch (error) {
                            return ChildRouter.responseBHCallBack(req, res, {
                                RspCode: '99',
                                Message: 'Unknow error'
                            });
                        }
                    }]
                },
            },
            '/pvi_callback_sandbox': {
                config: {
                    auth: [this.roles.all],
                    post: 'json'
                },

                methods: {
                    post: [async function (req, res) {
                        try {
                            let {
                                CardId,
                                SerialNumber,
                                RequestId,
                                PolicyNumber,
                                URL,
                                CpId,
                                Sign,
                            } = req.body;

                            return ChildRouter.responseBHCallBack(req, res, {...req.body});
                        } catch (error) {
                            return ChildRouter.responseBHCallBack(req, res, {
                                RspCode: '99',
                                Message: 'Unknow error'
                            });
                        }
                    }]
                },
            },
        }
    }
};
