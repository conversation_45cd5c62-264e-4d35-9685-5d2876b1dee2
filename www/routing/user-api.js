"use strict";

const promise = require('bluebird');
const ChildRouter = require('../config/router/ChildRouting');
const UserModel = require('../models/UserModel');
const CommentModel = require('../models/CommentModel');
const CodePasswordModel = require('../models/CodePasswordModel');
const AdsSettingModel = require('../models/AdsSettingModel');
const MailUser = require('../mailer/module/MailUser');
const CfJws = require('../config/CfJws');
const CategoryModel = require('../models/CategoryModel');
const ProducModel = require('../models/ProducModel');
const NewsModel = require('../models/NewsModel');
const MessageModel = require('../models/MessageModel');
const ServicesModel = require('../models/ServicesModel');
const ServiceOfSpaModel = require('../models/ServiceOfSpaModel');
const ServiceOfClinicModel = require('../models/ServiceOfClinicModel');
const RoomOfHotelModel = require('../models/RoomOfHotelModel');
const ClassificationOfBrandModel = require('../models/ClassificationOfBrandModel');
const ObjectId = require('mongoose').Types.ObjectId;
const BookRoomModel = require('../models/BookRoomModel');
const BookExaminationModel = require('../models/BookExaminationModel');
const BookSpaModel = require('../models/BookSpaModel');
const NotificationModel = require('../models/NotificationModel');
const AccountBankModel = require('../models/AccountBankModel');
const RequestBuyProductModel = require('../models/RequestBuyProductModel');
const PromotionModel = require('../models/PromotionModel');
const ProductModel = require('../models/ProducModel');
const FileUtils = require('../utils/FileUtils');
const StringUtils = require('../utils/StringUtils');
const UserSession = require('../session/UserSession');
const htmlspecialchars = require('htmlspecialchars');
const SettingAdminModel = require('../models/SettingAdminModel');
const FundLogModel = require('../models/FundLogModel');
const TimKiemModal = require('../models/TimKiemModal');
const UserSettingModel = require('../models/UserSettingModel');
const CouponModel = require('../models/CouponModel');
const CouponUserModel = require('../models/CouponUserModel');
const HoTroModel = require('../models/HoTroModel');
const WalletOnlineModel = require('../models/WalletOnlineModel');
const CarModel = require('../models/CarModel');
const PetLogModel = require('../models/PetLogModel');
const PetGalleryModel = require('../models/PetGalleryModel');
const PetCategoryModel = require('../models/PetCategoryModel');
const GHTK = require('../utils/GiaoHang/ghtk');
const {v4} = require('uuid');
const moment = require('moment');
const CfVpnPay = require('../config/CfVpnPay');
const querystring = require('qs');
const sha256 = require('sha256');
const VpnPayModel = require('../models/VpnPayModel');
const CodeEmailModel = require('../models/CodeEmailModel');
const BannerAppModel = require('../models/BannerAppModel');
const AdsStoreModel = require('../models/AdsStoreModel');
const BranchModel = require('../models/BranchModel');
const BookingUtil = require("../utils/BookingUtil");
const CouponUtil = require("../utils/CouponUtil");
const {ProductOrderStatus} = require("../models/enum/ProductOrderStatus");
const {firebaseAdmin} = require("../config/CfFirebaseAdmin");
const lodash = require('lodash');
const pathFile = require("path");
const base64Img = require("base64-img");
const fs = require('fs');
const CfApp = require("../config/CfApp");
const {getProductId, md5} = require("../utils/StringUtils");
const ValidateUtil = require("../utils/ValidateUtil");
const ProvinceModel = require('../models/ProvinceModel');
const RocketChatUtils = require('../utils/RocketChatUtils')
const MapUtil = require('../utils/MapUtil');
const ShippingModel = require('../models/ShippingModel');
const UserViewedModel = require('../models/UserViewedModel');
const ProductRateModel = require('../models/ProductRateModel');
const {TypeServices} = require("../models/enum/TypeServices");
const {getNumberPrice} = require("../utils/StringUtils");
const BookClassificationModel = require("../models/BookClassificationModel");
const UserPointLogModel = require("../models/UserPointLogModel");
const PointUtil = require("../utils/PointUtil");
const axios = require("axios");
const {apiUrlV2} = require("../config/CfApp");
const DataSearchModel = require("../models/DataSearchModel");
const _ = require("lodash");
const Notification = require("../utils/Notification");
const FcmTokensModel = require("../models/FcmTokensModel");
const {getRemoteConfig} = require("../utils/firebaseRemoteConfigUtil");
const {bicLogin, buildHeaderBic} = require("../utils/BaoHiemUtil");
const {token} = require("../config/CfRocketChat");
const {roundUp} = require("../utils/NumberUtils");
var FormData = require('form-data');
const {loggerUtil} = require("../utils/loggerUtil");
axios.interceptors.request.use(request => {
    loggerUtil.info(request.url, {
        request: request?.data
    });
    return request
})

module.exports = class Auth extends ChildRouter {

    constructor() {
        super('/user/api');
    }

    registerRouting(io) {
        return {
            '/register-social.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                // {key: 'fullName', type: this.dataType.string, name: 'Họ và tên', min: 5, max: 200},
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {fullName, phone, picture, facebookId, googleId, appleId} = req.body;
                        let user;
                        if (facebookId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({facebookId});
                        }

                        if (googleId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({googleId});
                        }

                        if (appleId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({appleId});
                        }
                        if (user) {
                            return ChildRouter.responseError("Tài khoản đã tồn tại không thể đăng ký", res);
                        } else {
                            let result = await UserModel.MODEL.registerUser({
                                fullName, email: '', picture, phone, facebookId, googleId, type: 2, appleId
                            });

                            if (result.error) return ChildRouter.responseError(result.message, res);
                            let customFields = {
                                code: result.user.code,
                            };
                            // let objUser = {
                            //     username: result.user.phone.replace('+', ''),
                            //     email: result.user.email ? result.user.email : result.user.phone + '@yourmail.com',
                            //     password: result.user._id.toString(),
                            //     name: fullName,
                            //     roles: ['user'],
                            //     customFields
                            // }
                            //
                            // try {
                            //     let rs = await RocketChatUtils.registerUser(objUser);
                            // } catch (e) {
                            //     console.error(e)
                            // }

                            user = result.user;
                        }

                        if (user) {
                            let token = CfJws.createToken(user);
                            return ChildRouter.responseSuccess("Đăng ký thành công", res, {user, token});
                        } else {
                            return ChildRouter.responseError("Tài khoản không tồn tại", res);
                        }
                    }],
                },
            },
            '/dang-nhap-social.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                // {key: 'fullName', type: this.dataType.string, name: 'Họ và tên', min: 5, max: 200},
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {fullName, phone, picture, facebookId, googleId, appleId} = req.body;
                        let user;
                        if (facebookId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({facebookId});
                        }

                        if (googleId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({googleId});
                        }

                        if (appleId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({appleId});
                        }
                        if (user) {
                            if (user.type != 2) return ChildRouter.responseError("Ứng dụng chỉ dành cho tài khoản người dùng", res);
                        }

                        if (user) {
                            if (user && user.status == 2) return ChildRouter.responseError("Tài khoản đã bị khoá", res);
                            let token = CfJws.createToken(user);
                            return ChildRouter.responseSuccess("Đăng nhập thành công", res, {user, token});
                        } else {
                            return ChildRouter.responseError("Tài khoản không tồn tại", res);
                        }
                    }],
                },
            },
            '/dang-ky.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'fullName', type: this.dataType.string, name: 'Họ và tên', min: 5, max: 20},
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 20},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {fullName, email, password} = req.body;
                        let result = await UserModel.MODEL.registerUser({
                            fullName: fullName,
                            email: email,
                            password,
                            type: 2,
                        });
                        if (result.error) return ChildRouter.responseError(result.message, res);
                        let user = result.user;
                        if (user.confirmEmail == 1) {
                            let token = CfJws.createToken(user);
                            return ChildRouter.responseSuccess("Đăng ký thành công", res, {
                                user,
                                token
                            });
                        } else {
                            return ChildRouter.responseSuccess("Đăng ký thành công. Vui lòng kiểm tra và xác nhận email để đăng nhập", res);
                        }
                    }],
                },
            },
            '/create-sms.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'phone', type: this.dataType.string, name: 'Số điện thoại', min: 9, max: 15},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {phone} = req.body;
                        // var accountSid = '**********************************'; // Your Account SID from www.twilio.com/console
                        // var authToken = '0ebee904fa5cb4fad55ea71019127cbd';   // Your Auth Token from www.twilio.com/console
                        var accountSid = '**********************************'; // Your Account SID from www.twilio.com/console
                        var authToken = '98985d268319db6b1cee18801f2f1815';   // Your Auth Token from www.twilio.com/console
                        var twilio = require('twilio');
                        var client = new twilio(accountSid, authToken);

                        client.messages.create({
                            body: 'Mã xác thực của bạn là 12345',
                            to: phone,  // Text this number
                            from: '+***********' // From a valid Twilio number
                        }).then((message) => {
                            console.log(message.sid)
                        }).catch(err => {
                            console.log(err)
                        })
                        return ChildRouter.responseSuccess("Đăng ký thành công", res, {});
                    }],
                },
            },
            '/register-by-phone.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'phone', type: this.dataType.string, name: 'Số điện thoại', min: 9, max: 12},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 20},
                                {key: 'fullName', type: this.dataType.string, name: 'Họ và tên'},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {email, phone, password, fullName, picture, facebookId, googleId, appleId} = req.body;
                        //1. đăng ký tài khoản theo số điện thoại
                        let result = await UserModel.MODEL.registerUser({
                            email: email ?? '',
                            phone: phone,
                            password,
                            type: 2,
                            fullName: fullName ?? '',
                            picture,
                            facebookId,
                            googleId,
                            appleId
                        });

                        // //2. đăng ký số tài khoản rocketchat
                        // if (result && result.user) {
                        //
                        //     let customFields = {
                        //         code: result.user.code,
                        //     };
                        //     let objUser = {
                        //         username: result.user.phone.replace('+', ''),
                        //         email: result.user.email ? result.user.email : result.user.phone + '@yourmail.com',
                        //         password: result.user._id.toString(),
                        //         name: fullName,
                        //         roles: ['user'],
                        //         customFields
                        //     }
                        //     console.log({objUser})
                        //     try {
                        //         let rs = await RocketChatUtils.registerUser(objUser);
                        //     } catch (e) {
                        //         console.error(e)
                        //     }
                        // }
                        if (result.error) return ChildRouter.responseError(result.message, res);
                        let user = result.user;
                        let token = CfJws.createToken(user);
                        return ChildRouter.responseSuccess("register_success", res, {
                            user,
                            token
                        });
                    }],
                },
            },
            '/dang-nhap.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 50},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {email, password} = req.body;
                        let result = await UserModel.MODEL.signInAccount(email, password);
                        if (result.error) return ChildRouter.responseError(result.message, res);
                        let user = result.user;
                        if (user.type != 2) return ChildRouter.responseError("Tài khoản không tồn tại", res);
                        if (user.status == 2) return ChildRouter.responseError("Tài khoản đã bị khoá", res);
                        if (user.confirmEmail == 0 || user.confirmEmail == 1) {
                            if (user.confirmEmail == 0) return ChildRouter.responseError("Tài khoản chưa xác nhận email. Vui lòng kiểm tra email và xác nhận trước khi đăng nhập", res);
                        }
                        let token = CfJws.createToken(user);
                        return ChildRouter.responseSuccess("Đăng nhập thành công", res, {user, token});
                    }],
                },
            },
            '/login.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'phone', type: this.dataType.string, name: 'Phone', min: 9, max: 12},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 50},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {phone, email, password, facebookId, googleId, appleId} = req.body;

                        // nếu truyền socialId thì đăng nhập = socialId trước

                        let user;
                        if (facebookId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({facebookId});
                        } else if (googleId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({googleId});
                        } else if (appleId) {
                            user = await UserModel.MODEL.getOneUsersByCondition({appleId});
                        } else if (phone && password) {
                            // dang nhap = phone + password
                            let result = await UserModel.MODEL.signInAccountByPhone(phone, password);
                            if (result.error) return ChildRouter.responseError(result.message, res);
                            user = result.user;
                        } else if (email && password) {
                            // dang nhap = email + password
                            let result = await UserModel.MODEL.signInAccount(email, password);
                            if (result.error) return ChildRouter.responseError(result.message, res);
                            user = result.user;
                        }

                        if (user) {
                            if (user.type != 2) return ChildRouter.responseError("err_account_does_not_exist", res);
                            if (user.status == 2) return ChildRouter.responseError("Tài khoản đã bị khoá", res);
                            // if (user.confirmPhone == 0 || user.confirmPhone == 1) {
                            //     if (user.confirmPhone == 0) return ChildRouter.responseError("Tài khoản chưa xác nhận email. Vui lòng kiểm tra email và xác nhận trước khi đăng nhập", res);
                            // }
                        }


                        let token = CfJws.createToken(user);
                        let rocketChatData = null;
                        if (user.phone) {
                            let userPhone = user.phone.replace('+', '');
                            let rocketLoginData = {
                                user: userPhone,
                                password: user._id.toString()
                            }
                            // console.log({rocketLoginData})
                            //
                            // try {
                            //     let result = await RocketChatUtils.login(rocketLoginData);
                            //     if (result.status == 200) {
                            //         rocketChatData = result.data;
                            //     } else {
                            //         return ChildRouter.responseError("Không thể đăng nhập rocketChat", res, result);
                            //     }
                            // } catch (e) {
                            //     return ChildRouter.responseError("Không thể đăng nhập rocketChat", res, e);
                            // }
                        }

                        user.rocketChatData = rocketChatData;
                        return ChildRouter.responseSuccess("Đăng nhập thành công", res, {user, token});
                    }],
                },
            },
            '/forgot-password.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'phone', type: this.dataType.string, name: 'Phone', min: 9, max: 12},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {phone} = req.body;
                        let user = await UserModel.MODEL.getOneUsersByCondition({phone});
                        if (user) {
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Số điện thoại không tồn tại trong hệ thống!', res);
                        }
                    }],
                },
            },
            '/recover-password.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'phone', type: this.dataType.string, name: 'Phone', min: 9, max: 12},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 200},
                                {
                                    key: 'confirmPassword',
                                    type: this.dataType.string,
                                    name: 'Nhập lại mật khẩu',
                                    min: 5,
                                    max: 200
                                },
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {password, confirmPassword, phone} = req.body;
                        if (password != confirmPassword) {
                            return ChildRouter.responseError("Nhập lại mật khẩu không đúng", res);
                        }
                        let rsl = await UserModel.MODEL.updateUserByPhone(phone, {password});
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        return ChildRouter.responseSuccess("Thay đổi mật khẩu thành công", res);
                    }]
                },
            },
            '/quen-mat-khau.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {email} = req.body;
                        let user = await UserModel.MODEL.getOneUsersByCondition({email});
                        if (user) {
                            if (user.type != 2) return ChildRouter.responseError("Tài khoản không tồn tại", res);
                            let code = await CodePasswordModel.MODEL.randomCodeEmail(user._id, user.email, 5);
                            await MailUser.sendCodeResetPassword(user.email, code);
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Email không tồn tại trong hệ thống!', res);
                        }
                    }],
                },
            },
            '/lay-lai-mat-khau.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                                {key: 'code', type: this.dataType.string, name: 'Mã', min: 5, max: 200},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 200},
                                {
                                    key: 'confirmPassword',
                                    type: this.dataType.string,
                                    name: 'Nhập lại mật khẩu',
                                    min: 5,
                                    max: 200
                                },
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {password, confirmPassword, code, email} = req.body;
                        if (password != confirmPassword) {
                            return ChildRouter.responseError("Nhập lại mật khẩu không đúng", res);
                        }

                        let dataCode = await CodePasswordModel.MODEL.getCode(email.toLowerCase(), code);
                        if (dataCode) {
                            if (new Date().getTime() < (new Date(dataCode.createAt).getTime() + (60 * 24 * 60 * 1000))) {
                                let rsl = await UserModel.MODEL.updateUserByEmail(email, {password});
                                if (rsl.error) {
                                    return ChildRouter.response(res, rsl);
                                }
                                return ChildRouter.responseSuccess("Thay đổi mật khẩu thành công", res);
                            } else {
                                return ChildRouter.responseError("Yêu cầu không hợp lệ", res);
                            }
                        } else {
                            return ChildRouter.responseError("Yêu cầu không hợp lệ", res);
                        }
                    }]
                },
            },
            '/request-lock-account': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'phone', type: this.dataType.string, name: 'Phone', min: 9, max: 12},
                                {key: 'password', type: this.dataType.string, name: 'Mật khẩu', min: 5, max: 50},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {phone, email, password} = req.body;
                        let user = null
                        if (phone && password) {
                            // dang nhap = phone + password
                            let result = await UserModel.MODEL.signInAccountByPhone(phone, password);
                            if (result.error) return ChildRouter.responseError(result.message, res);
                            user = result.user;
                        } else if (email && password) {
                            // dang nhap = email + password
                            let result = await UserModel.MODEL.signInAccount(email, password);
                            if (result.error) return ChildRouter.responseError(result.message, res);
                            user = result.user;
                        }

                        if (user) {
                            if (user.type !== 2) return ChildRouter.responseError("err_account_does_not_exist", res);
                            if (user.status === 2) return ChildRouter.responseError("Tài khoản đã bị khoá", res);
                            const rs = await UserModel.MODEL.updateUser(req.user._id, {status: 2});
                            return ChildRouter.responseSuccess("Yêu cầu khoá tài khoản thành công", res, {user});
                        }
                        return ChildRouter.responseError("Yêu cầu thất bại", res);
                    }],
                },
            },
            '/check-token.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json'
                },

                methods: {
                    post: [async function (req, res) {
                        let {token} = req.body;
                        CfJws.extraToken(token).then(async (rslCheckToken) => {
                            if (rslCheckToken.error) {
                                return ChildRouter.responseError('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại', res);
                            } else {
                                if (rslCheckToken && rslCheckToken.data) {
                                    let user = await UserModel.MODEL.getUsersById(rslCheckToken.data._id);
                                    delete user.password;
                                    if (user.status == 2) {
                                        return ChildRouter.responseError('Tài khoản của bạn đã bị khoá', res);
                                    } else {
                                        // let token = CfJws.createToken(user);
                                        return ChildRouter.responseSuccess('Thành công', res, {user, token});
                                    }
                                } else {
                                    return ChildRouter.responseError('Cần đăng nhập lại', res);

                                }
                            }
                        })
                    }]
                },
            },

            '/trang-chu.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({status: 1});
                        let productSells = await ProducModel.MODEL.getProductByConditionSell({status: 1}, {revenue: -1}, 20);
                        let dataNews = await NewsModel.MODEL.getNewsByConditionNear({}, 4);
                        return ChildRouter.responseSuccess("Thành công", res, {categories, productSells, dataNews});
                    }],
                },
            },
            '/tin-tuc.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({status: 1});
                        let productSells = await ProducModel.MODEL.getProductByConditionSell({status: 1}, {revenue: -1}, 20);
                        let dataNews = await NewsModel.MODEL.getNewsByConditionNear({}, 20);
                        return ChildRouter.responseSuccess("Thành công", res, {categories, productSells, dataNews});
                    }],
                },
            },

            '/san-pham-theo-danh-muc/:categoryId.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {page} = req.query;
                        if (!page) page = 1;
                        let limit = 10;
                        let productSells = await ProducModel.MODEL.getProductForCategory(req.params.categoryId, page, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {productSells});
                    }],
                },
            },

            '/tim-kiem-san-pham.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, categoryId, page} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        let limit = 5;
                        let result = await ProducModel.MODEL.searchProduct(search, categoryId, page, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {result});
                    }],
                },
            },

            '/chi-tiet-san-pham/:productId.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let product = await ProducModel.MODEL.getProductsById(req.params.productId);
                        let productRelates = [];
                        if (product && product.categoryId) {
                            productRelates = await ProducModel.MODEL.getProductByConditionSell({
                                categoryId: product.categoryId,
                                status: 1,
                                _id: {$ne: ObjectId(product._id)}
                            }, {watched: -1, createAt: -1}, 40);
                        }

                        return ChildRouter.responseSuccess("Thành công", res, {product, productRelates});
                    }],
                },
            },

            '/shopping.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {
                            categoriesCho,
                            categoriesMeo,
                            categoriesThuCungKhac
                        } = await CategoryModel.MODEL.getCategoryGroupType({});
                        let productSells = await ProducModel.MODEL.getProductByConditionSell({status: 1}, {revenue: -1}, 40);
                        let productNears = await ProducModel.MODEL.getProductByConditionSell({status: 1}, {createAt: -1}, 40);
                        return ChildRouter.responseSuccess("Thành công", res, {
                            productSells,
                            productNears,
                            categoriesCho,
                            categoriesMeo,
                            categoriesThuCungKhac
                        });
                    }],
                },
            },

            '/get-products-for-pet': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {
                            pet,
                            page,
                            limit
                        } = req.query;
                        pet = Number(pet) || 0;
                        limit = Number(limit) || 20;
                        page = Number(page) || 1;
                        let catIds = [];

                        let categories = await CategoryModel.MODEL.getCategoryByType(pet);
                        if (categories) {
                            catIds = categories.map(cat => {
                                return cat._id.toString();
                            })
                        }
                        let data = await ProductModel.MODEL.getProductPageByCatIds(catIds, page, limit);

                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },

            /**
             * type tair tin nhắn chưa đọc
             */
            '/tin-nhan.html/:type': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let condition = {$or: [{userId: req.user._id}, {userSendId: req.user._id}]};
                        if (Number(req.params.type) == 1) {
                            condition.watched = 0
                        }
                        let dataMessages = await MessageModel.MODEL.getMessageByCondition(condition, {createAt: -1});
                        let dataServices = {};
                        dataMessages.forEach(item => {
                            if (item.userSendId == req.user._id) {
                                if (!dataServices[item.userId]) dataServices[item.userId] = item
                            } else {
                                if (!dataServices[item.userSendId]) dataServices[item.userSendId] = item
                            }

                        });
                        let objects = [];
                        let mapFunc = [];
                        for (let key in dataServices) {
                            mapFunc.push(new promise(async resolve => {
                                let store = await UserModel.MODEL.getUsersById(key);
                                dataServices[key].store = {
                                    _id: store._id,
                                    fullName: store.fullName,
                                    picture: store.picture,
                                };
                                delete dataServices[key].store.password;
                                dataServices[key].watched = dataServices[key].userSendId == req.user._id ? 1 : dataServices[key].watched;
                                if (Number(req.params.type) == 1 && dataServices[key].watched == 1) {
                                    return resolve();
                                }

                                objects.push(dataServices[key]);
                                return resolve();
                            }))
                        }
                        await promise.all(mapFunc);

                        function compare(a, b) {
                            if (a.createAt < b.createAt) {
                                return 1;
                            }
                            if (a.createAt > b.createAt) {
                                return -1;
                            }
                            return 0;
                        }

                        objects.sort(compare);
                        return ChildRouter.responseSuccess("Thành công", res, {objects});
                    }],
                },
            },

            '/gui-tin-nhan-anh.html/:to': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 10}],
                },

                methods: {
                    post: [async function (req, res) {
                        let {to} = req.params;

                        function insertMessage(picture) {
                            return new promise(async resolve => {
                                let chat = await MessageModel.MODEL.addMessage({
                                    userId: to,
                                    userSendId: req.user._id,
                                    content: 'Đã gửi ảnh',
                                    picture: picture.path,
                                    height: picture.height,
                                    width: picture.width,
                                    type: 1
                                });
                                chat.fullName = req.user.fullName;
                                chat.userPicture = req.user.picture;
                                let data = {chat, type: 'new-message'};
                                setTimeout(async () => {
                                    await UserModel.MODEL.updateUser(to, {$inc: {countMessage: 1}});
                                    let newInfo = await UserModel.MODEL.getUsersById(to);
                                    io.emit(to, {
                                        type: 'change-count-message',
                                        countMessage: newInfo.countMessage
                                    });
                                }, 500);


                                io.emit(to, data);
                                io.emit(req.user._id, data);
                                setTimeout(() => {
                                    return resolve();
                                }, 800);
                            })
                        }

                        if (req.upload && req.upload.picture) {
                            req.upload.picture.forEach(async p => {
                                await insertMessage(p);
                            });
                            return ChildRouter.responseSuccess("Thành công", res);
                        } else {
                            return ChildRouter.responseError("Ảnh không hợp lệ", res);
                        }
                    }],
                },
            },

            '/tin-nhan-cua-hang/:servicesId/:lastMessageId.html': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {lastMessageId, servicesId} = req.params;
                        if (lastMessageId == 'null') lastMessageId = '';
                        let condition = {
                            $or: [{
                                userId: req.user._id,
                                userSendId: servicesId
                            }, {userSendId: req.user._id, userId: servicesId}]
                        };

                        let service = null;
                        if (lastMessageId != '') {
                            condition._id = {$lt: ObjectId(lastMessageId)}
                        } else {
                            service = await UserModel.MODEL.getUsersById(servicesId);
                            delete service.password;
                        }

                        let messages = await MessageModel.MODEL.getMessageByCondition(condition, {createAt: -1}, 500);
                        await MessageModel.MODEL.updateMessageByConditon({userId: req.user._id}, {watched: 1});
                        return ChildRouter.responseSuccess("Thành công", res, {messages, service});
                    }],
                },
            },

            '/cap-nhap-tai-khoan.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'fullName', type: this.dataType.string, name: 'Họ và tên', min: 5, max: 200},
                                {key: 'email', type: this.dataType.string, name: 'Email', min: 5, max: 200},
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            fullName, birthday, phone, email, sex, description, namePet, typePet,
                            province,
                            district,
                            ward,
                            street,
                            location
                        } = req.body;
                        let address = (location || '') + ' ' + (street || '') + ', ' + (ward || '') + ', ' + (district || '') + ', ' + (province || '');
                        if (address.split(",").join("").trim() == '') {
                            address = '';
                        }

                        let obj = {
                            fullName,
                            phone,
                            email,
                            province,
                            district,
                            ward,
                            street,
                            location,
                            address,
                            sex: Number(sex),
                            typePet: typePet,
                            namePet,
                            description
                        };
                        for (let key in obj) {
                            if (!obj[key] && obj[key] != 0) {
                                delete obj[key]
                            }
                        }
                        if (!isNaN(new Date(birthday).getTime())) {
                            obj.birthday = new Date(birthday).getTime()
                        }
                        let rsl = await UserModel.MODEL.updateUser(req.user._id, obj);
                        if (rsl.error) {
                            return ChildRouter.responseError(rsl.message, res);
                        }
                        let resConfirmEmail = rsl.resConfirmEmail;
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        delete user.password;
                        let token = CfJws.createToken(user);
                        return ChildRouter.responseSuccess("Thành công", res, {user, token, resConfirmEmail});
                    }]
                },
            },

            '/thay-doi-mat-khau.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'currentPass',
                                    type: this.dataType.string,
                                    name: 'Mật khẩu hiện tại',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'newPass',
                                    type: this.dataType.string,
                                    name: 'Mật khẩu mới',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'newPassConf',
                                    type: this.dataType.string,
                                    name: 'Nhập lại mật khẩu mới',
                                    min: 5,
                                    max: 200
                                },
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {currentPass, newPass, newPassConf} = req.body;
                        if (newPass != newPassConf) return ChildRouter.responseError("Nhập lại mật khẩu không khớp", res);
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        if (user.password != StringUtils.md5(currentPass)) return ChildRouter.responseError("Mật khẩu hiện tại không đúng", res);
                        let rsl = await UserModel.MODEL.updateUser(req.user._id, {password: newPass});
                        if (rsl.error) {
                            return ChildRouter.response(res, rsl);
                        }
                        return ChildRouter.responseSuccess("Thay đổi mật khẩu thành công", res);
                    }]
                },
            },

            '/update-avatar.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 1}],
                },

                methods: {
                    post: [async function (req, res) {
                        if (req.upload && req.upload.picture) {

                            if (req.user.picture && req.user.picture.includes('files')) {
                                let user = await UserModel.MODEL.getUsersById(req.user._id);
                                const path = '/files' + user.picture.split('files')[1]
                                FileUtils.deleteFileDropBox(path)
                            }

                            await UserModel.MODEL.updateUser(req.user._id, {picture: req.upload.picture[0].path});
                            return ChildRouter.responseSuccess("Thành công", res, {picture: req.upload.picture[0].path});
                        } else {
                            return ChildRouter.responseError("Ảnh không hợp lệ", res);
                        }
                    }]
                },
            },
            '/update-profile.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'phone', type: this.dataType.string, name: 'Số điện thoại!', min: 9, max: 12},
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {
                            fullName,
                            birthday,
                            email,
                            sex,
                            description,
                            namePet,
                            typePet,
                            picture,
                            phone,
                            addressList,
                        } = req.body;

                        // let address = (location || '') + ' ' + (street || '') + ', ' + (ward || '') + ', ' + (district || '') + ', ' + (province || '');
                        // if (address.split(",").join("").trim() == '') {
                        //     address = '';
                        // }

                        let obj = {
                            fullName,
                            phone,
                            email,
                            birthday,
                            addressList,
                            sex: Number(sex),
                            typePet: typePet,
                            namePet,
                            description,
                            picture,
                        };
                        for (let key in obj) {
                            if (!obj[key] && obj[key] != 0) {
                                delete obj[key]
                            }
                        }
                        if (!obj['addressList']) {
                            obj.addressList = []
                        }
                        try {
                            const day = parseInt(birthday.split('/')[0])
                            const month = parseInt(birthday.split('/')[1])
                            const year = parseInt(birthday.split('/')[2])
                            obj.birthday = new Date(year, month - 1, day).getTime()
                        } catch (e) {

                        }
                        let rsl = await UserModel.MODEL.updateUser(req.user._id, obj);
                        if (rsl.error) {
                            return ChildRouter.responseError(rsl.message, res);
                        }
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        delete user.password;
                        let token = CfJws.createToken(user);
                        return ChildRouter.responseSuccess("Thành công", res, {user, token});
                    }]
                },
            },
            '/cap-nhap-background.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 1}],
                },

                methods: {
                    post: [async function (req, res) {
                        if (req.upload && req.upload.picture) {
                            if (req.user.background && req.user.background.includes('files')) {
                                FileUtils.deleteFile("." + req.user.background);
                            }

                            await UserModel.MODEL.updateUser(req.user._id, {background: req.upload.picture[0].path});
                            let user = await UserModel.MODEL.getUsersById(req.user._id);
                            delete user.password;
                            let token = CfJws.createToken(user);
                            return ChildRouter.responseSuccess("Thành công", res, {user, token});
                        } else {
                            return ChildRouter.responseError("Ảnh không hợp lệ", res);
                        }
                    }]
                },
            },

            '/khach-san.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let uyTin = await ServicesModel.MODEL.getServicesByCondition({
                            businessTypes: {$in: ['2']},
                            status: 1
                        }, {
                            luotDangKy: -1,
                            createAt: -1
                        }, 10);

                        let khac = await ServicesModel.MODEL.getServicesByCondition({
                            businessTypes: {$in: ['2']},
                            status: 1
                        }, {
                            createAt: -1
                        }, 10);
                        return ChildRouter.responseSuccess("Thành công", res, {uyTin, khac});
                    }],
                },
            },

            '/dat-khach-san.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'email',
                                    type: this.dataType.string,
                                    name: 'Email',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'phone',
                                    type: this.dataType.string,
                                    name: 'Số diện thoại',
                                    min: 10,
                                    max: 15
                                },
                                {
                                    key: 'petSex',
                                    type: this.dataType.string,
                                    name: 'Loại thú cưng',
                                    in: ['male', 'female'],
                                },
                                {
                                    key: 'typePet',
                                    type: this.dataType.number,
                                    name: 'Loại thú cưng',
                                    in: [0, 1, 2],
                                },
                                {
                                    key: 'weight',
                                    type: this.dataType.string,
                                    name: 'Cân nặng',
                                    min: 1,
                                    max: 10
                                },
                                {
                                    key: 'startTime',
                                    type: this.dataType.string,
                                    name: 'Thời gian nhận phòng',
                                    min: 5,
                                    max: 100
                                }, {
                                    key: 'endTime',
                                    type: this.dataType.string,
                                    name: 'Thời gian trả phòng',
                                    min: 5,
                                    max: 100
                                }, {
                                    key: 'note',
                                    type: this.dataType.string,
                                    name: 'Ghi chú',
                                    min: 1,
                                    max: 500
                                }, {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Điểm gửi xe',
                                    min: 1,
                                    max: 500
                                }, {
                                    key: 'countTime',
                                    type: this.dataType.string,
                                    name: 'Thời gian ở theo ngày',
                                    min: 1,
                                    max: 500
                                }, {
                                    key: 'countRooms',
                                    type: this.dataType.number,
                                    name: 'Số phòng',
                                    min: 1,
                                    max: 11
                                }, {
                                    key: 'datePrice',
                                    type: this.dataType.number,
                                    name: 'Giá/ngày',
                                    min: 0,
                                    max: 9999999999
                                },
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            storeId, startTime, endTime, petSex, weight, note, phone, email, typePet,
                            datePrice, countRooms, countTime
                        } = req.body;
                        if (new Date(startTime).getTime() < new Date().getTime()) {
                            return ChildRouter.responseError("Thời gian nhận phòng phải lớn hơn thời gian hiện tại", res);
                        }

                        ServicesModel.MODEL.tangLuotDangKy(storeId);
                        let store = await ServicesModel.MODEL.getServicesById(storeId);
                        weight = weight.replace('Kg', '');
                        weight = weight.replace('kg', '');
                        weight = weight.trim();
                        let book = await BookRoomModel.MODEL.addBookRoom({
                            userId: req.user._id,
                            storeId,
                            storeManagerId: store.userId,
                            timeCheckIn: new Date(startTime).getTime(),
                            timeCheckOut: new Date(endTime).getTime(),
                            petSex,
                            weight,
                            note,
                            phone, email, typePet,
                            dateTime: Number(countTime),
                            countRooms: Number(countRooms),
                            datePrice: Number(datePrice),
                        });
                        NotificationModel.MODEL.addNewNotification(store.userId, req.user._id, "User đặt dịch vụ", 1, io, {
                            bookId: book._id,
                            typeService: 2,
                            orderId: book.orderId,
                            storeId
                        });

                        return ChildRouter.responseSuccess("Đặt phòng thành công", res);
                    }],
                },
            },

            '/phong-kham.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let uyTin = await ServicesModel.MODEL.getServicesByCondition({
                            businessTypes: {$in: ['1']},
                            status: 1
                        }, {
                            luotDangKy: -1,
                            createAt: -1
                        }, 10);

                        let khac = await ServicesModel.MODEL.getServicesByCondition({
                            businessTypes: {$in: ['1']},
                            status: 1
                        }, {
                            createAt: -1
                        }, 10);
                        return ChildRouter.responseSuccess("Thành công", res, {uyTin, khac});
                    }],
                },
            },

            '/dat-phong-kham.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'email',
                                    type: this.dataType.string,
                                    name: 'Email',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'phone',
                                    type: this.dataType.string,
                                    name: 'Số diện thoại',
                                    min: 10,
                                    max: 15
                                },
                                {
                                    key: 'petSex',
                                    type: this.dataType.string,
                                    name: 'Loại thú cưng',
                                    in: ['male', 'female'],
                                },
                                {
                                    key: 'typePet',
                                    type: this.dataType.number,
                                    name: 'Loại thú cưng',
                                    in: [0, 1, 2],
                                },
                                {
                                    key: 'weight',
                                    type: this.dataType.string,
                                    name: 'Cân nặng',
                                    min: 1,
                                    max: 10
                                },
                                {
                                    key: 'time',
                                    type: this.dataType.string,
                                    name: 'Thời gian đến khám',
                                    min: 5,
                                    max: 100
                                }, {
                                    key: 'symptom',
                                    type: this.dataType.string,
                                    name: 'Triệu chứng',
                                    min: 10,
                                    max: 500
                                }, {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Điểm gửi xe',
                                    min: 1,
                                    max: 500
                                },
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {storeId, time, petSex, weight, symptom, phone, email, typePet} = req.body;
                        if (new Date(time).getTime() < new Date().getTime()) {
                            return ChildRouter.responseError("Thời gian đến khám phải lớn hơn thời gian hiện tại", res);
                        }
                        weight = weight.replace('Kg', '');
                        weight = weight.replace('kg', '');
                        weight = weight.trim();

                        ServicesModel.MODEL.tangLuotDangKy(storeId);
                        let store = await ServicesModel.MODEL.getServicesById(storeId);

                        let book = await BookExaminationModel.MODEL.addBookExamination({
                            userId: req.user._id,
                            storeId,
                            storeManagerId: store.userId,
                            timeCheckIn: new Date(time).getTime(),
                            petSex,
                            weight,
                            symptom,
                            phone, email, typePet
                        });
                        NotificationModel.MODEL.addNewNotification(store.userId, req.user._id, "User đặt dịch vụ", 1, io, {
                            bookId: book._id,
                            typeService: 1,
                            orderId: book.orderId,
                            storeId
                        });

                        return ChildRouter.responseSuccess("Đặt lịch thành công", res);
                    }],
                },
            },

            '/spa.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let uyTin = await ServicesModel.MODEL.getServicesByCondition({
                            businessTypes: {$in: ['3']},
                            status: 1
                        }, {
                            luotDangKy: -1,
                            createAt: -1
                        }, 10);

                        let khac = await ServicesModel.MODEL.getServicesByCondition({
                            businessTypes: {$in: ['3']},
                            status: 1
                        }, {
                            createAt: -1
                        }, 10);
                        return ChildRouter.responseSuccess("Thành công", res, {uyTin, khac});
                    }],
                },
            },
            '/get-service-spa-by-storeid.html/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        // let spa = await ServiceOfSpaModel.MODEL.getServiceById(req.params.id);
                        let spa = await ServiceOfSpaModel.MODEL.getServicePageByCondition({
                            storeId: req.params.id,
                            status: 1
                        }, 1, 500);
                        let totalService = await ServiceOfSpaModel.MODEL.getServiceTotal({
                            storeId: req.params.id,
                            status: 1
                        })
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId: req.params.id})

                        if (req.headers['location']) {
                            const location = JSON.parse(req.headers['location']);
                            branchStore.forEach(branch => {
                                let objLocation = {
                                    lat: branch.lat,
                                    lng: branch.lng,
                                }
                                const distance = MapUtil.getDistance(location, objLocation);
                                branch.distance = parseFloat(distance);
                            });
                            branchStore = lodash.orderBy(branchStore, ['distance'], ['asc']);
                        }
                        return ChildRouter.responseSuccess("Thành công", res, {spa, totalService, branchStore});
                    }]
                }
            },
            '/get-service-spa-featured.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                }, methods: {
                    post: [async function (req, res) {
                        // let spa = await ServiceOfSpaModel.MODEL.getServiceById(req.params.id);
                        const {storeId, categoryId} = req.body
                        let filter = {isFeatured: true, status: 1}
                        if (storeId) {
                            filter['storeId'] = storeId
                        }
                        if (categoryId && categoryId !== '0') {
                            filter['categoryId'] = categoryId
                        }
                        let spa = await ServiceOfSpaModel.MODEL.getServicePageByCondition(filter, 1, 10);
                        let gara = await ServiceOfClinicModel.MODEL.getClinicPageByCondition(filter, 1, 10);
                        gara = gara ? gara : []
                        return ChildRouter.responseSuccess("Thành công", res, {spa: [...spa, ...gara]});
                    }]
                }
            },
            '/get-spa-id.html/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let spa = await ServicesModel.MODEL.getServicesById(req.params.id);
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId: req.params.id})
                        let userDetail = null
                        try {
                            userDetail = await UserModel.MODEL.getDataById(spa.userId)
                        } catch (e) {

                        }
                        return ChildRouter.responseSuccess("Thành công", res, {spa, branchStore, userDetail});
                    }]
                }
            },
            // API Get Room
            '/get-room-hotel-by-storeid.html/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let rooms = await RoomOfHotelModel.MODEL.getRoomPageByCondition({
                            storeId: req.params.id,
                            status: 1
                        }, 1, 500);
                        let totalRooms = await RoomOfHotelModel.MODEL.getRoomTotal({storeId: req.params.id, status: 1})
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId: req.params.id})
                        return ChildRouter.responseSuccess("Thành công", res, {rooms, totalRooms, branchStore});
                    }]
                }
            },
            // END API Get Room
            '/services-of-brand/:storeId/:classifyServiceType': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let condition = {
                            storeId: req.params.storeId,
                            classifyServiceType: req.params.classifyServiceType,
                            status: 1
                        }
                        let data = await ClassificationOfBrandModel.MODEL.getDataPageByCondition({
                            storeId: req.params.storeId,
                            classifyServiceType: req.params.classifyServiceType,
                            status: 1
                        }, 1, 500);
                        let total = await ClassificationOfBrandModel.MODEL.getTotal(condition)
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId: req.params.id})
                        return ChildRouter.responseSuccess("Thành công", res, {data, total, branchStore});
                    }]
                }
            }, // lấy tất cả dịch vụ mở rộng của thương hiệu
            // API Get Clinic
            '/get-clinic-by-storeid.html/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let clinics = await ServiceOfClinicModel.MODEL.getClinicPageByCondition({
                            storeId: req.params.id,
                            status: 1
                        }, 1, 500);
                        let totalClinics = await ServiceOfClinicModel.MODEL.getClinicTotal({
                            storeId: req.params.id,
                            status: 1
                        })
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId: req.params.id})
                        return ChildRouter.responseSuccess("Thành công", res, {clinics, totalClinics, branchStore});
                    }]
                }
            },
            // END API Get Clinic
            // API Get Product
            '/get-products-by-storeid.html/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let products = await ProductModel.MODEL.getProductPageByCondition({
                            storeId: req.params.id,
                            status: 1
                        }, 1, 500);
                        let totalProducts = await ProductModel.MODEL.getCountProductPageByCondition({
                            storeId: req.params.id,
                            status: 1
                        })
                        let branchStore = await BranchModel.MODEL.getBranchByCondition({storeId: req.params.id})
                        return ChildRouter.responseSuccess("Thành công", res, {products, totalProducts, branchStore});
                    }]
                }
            },
            // END API Get Product
            '/comment.html': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'userId',
                                    type: this.dataType.string,
                                    name: 'userId',
                                    min: 1,
                                    max: 255
                                },
                                {
                                    key: 'rate',
                                    type: this.dataType.number,
                                    name: 'Số sao',
                                    min: 0,
                                    max: 5
                                },
                                {
                                    key: 'content',
                                    type: this.dataType.string,
                                    name: 'Nội dung',
                                    min: 1,
                                    max: 255
                                }
                                //, { hien tai chua cho uploadanh
                                //     key: 'image',
                                //     type: this.dataType.string,
                                //     name: 'anh',
                                // }
                                , {
                                    key: 'like',
                                    type: this.dataType.number,
                                    name: 'Số lượt thích!',
                                }, {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Cửa Hàng',
                                    min: 1,
                                    max: 255
                                },
                            ]
                        }
                    }
                }, methods: {
                    post: [async function (req, res) {
                        let {storeId, userId, content, like, watched, rate} = req.body;
                        CommentModel.MODEL.addComment({
                            userId,
                            storeId,
                            content,
                            like,
                            watched,
                            rate
                        })
                        //gọi hàm tổng hợp số lượng rate example = 50
                        CommentModel.MODEL.getCommentRateTotal({storeId}).then(result => {
                            const totalRateValue = result.rateTotalValue
                            //call model update totalRate by storeId
                            ServicesModel.MODEL.updateServices(storeId, {totalRate: totalRateValue})
                        }).catch(err => {
                            console.log('Tong hop rate loi: ', err)
                        })
                        return ChildRouter.responseSuccess("comment thành công", res);
                    }]
                },
            },
            '/edit-comment.html/:id': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'userId',
                                    type: this.dataType.string,
                                    name: 'userId',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'content',
                                    type: this.dataType.string,
                                    name: 'Nội dung',
                                    min: 1,
                                    max: 255
                                }
                                //, { hien tai chua cho uploadanh
                                //     key: 'image',
                                //     type: this.dataType.string,
                                //     name: 'anh',
                                // }
                                , {
                                    key: 'like',
                                    type: this.dataType.number,
                                    name: 'Số lượt thích!',
                                },
                                {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Cửa Hàng',
                                    min: 1,
                                    max: 500
                                },
                            ]
                        }
                    }
                }, methods: {
                    post: [async function (req, res) {
                        let {storeId, userId, content, like, watched} = req.body;
                        CommentModel.MODEL.updateComment(req.params.id, {
                            userId,
                            storeId,
                            content,
                            like,
                            watched
                        })
                        return ChildRouter.responseSuccess("Sửa thành công", res);
                    }]
                },
            },
            '/delete-comment.html/:id': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        CommentModel.MODEL.deleteComment(req.params.id);
                        return ChildRouter.responseSuccess("Xóa comment thành công", res);
                    }]
                }
            },
            '/get-comment.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let {page, storeId, userId} = req.query;
                        // let storeId =req.body.storeId;
                        if (!page) page = 1;
                        page = Number(page);
                        let limit = 10;
                        let commentData = await CommentModel.MODEL.getCommentByConditionWithPage({storeId}, page, {createAt: -1}, limit, 'userId');
                        let conditionTotalRate = {storeId}
                        if (userId) {
                            conditionTotalRate['userId'] = userId
                        }
                        let totalRate = await CommentModel.MODEL.getCommentRateTotal({storeId})
                        let getRatingByUserId = await CommentModel.MODEL.getCommentByCondition(conditionTotalRate)
                        return ChildRouter.renderOrResponse(req, res, {
                            commentData,
                            totalRate,
                            storeId,
                            getRatingByUserId
                        });
                    }]
                }
            },
            '/get-all-comment-of-store.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let {page, storeId} = req.query;
                        // let storeId =req.body.storeId;
                        if (!page) page = 1;
                        page = Number(page);
                        let limit = 10;
                        let commentData = await CommentModel.MODEL.getCommentByConditionWithPage({storeId}, page, {createAt: -1}, limit, 'userId');
                        let totalPage = await CommentModel.MODEL.getTotalPage({storeId})
                        return ChildRouter.renderOrResponse(req, res, {commentData, totalPage});
                    }]
                }
            },
            '/get-comment-id.html/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                }, methods: {
                    get: [async function (req, res) {
                        let commentData = await CommentModel.MODEL.getCommentById(req.params.id);
                        return ChildRouter.responseSuccess("Thành công", res, {commentData});
                    }]
                }
            },

            '/dat-spa.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'email',
                                    type: this.dataType.string,
                                    name: 'Email',
                                    min: 5,
                                    max: 200
                                },
                                {
                                    key: 'phone',
                                    type: this.dataType.string,
                                    name: 'Số diện thoại',
                                    min: 10,
                                    max: 15
                                },
                                {
                                    key: 'petSex',
                                    type: this.dataType.string,
                                    name: 'Loại thú cưng',
                                    in: ['male', 'female'],
                                },
                                {
                                    key: 'typePet',
                                    type: this.dataType.number,
                                    name: 'Loại thú cưng',
                                    in: [0, 1, 2],
                                },
                                {
                                    key: 'weight',
                                    type: this.dataType.string,
                                    name: 'Cân nặng',
                                    min: 1,
                                    max: 10
                                },
                                {
                                    key: 'time',
                                    type: this.dataType.string,
                                    name: 'Thời gian đến khám',
                                    min: 5,
                                    max: 100
                                }, {
                                    key: 'service',
                                    type: this.dataType.string,
                                    name: 'Dịch vụ mong muốn',
                                    min: 1,
                                    max: 500
                                }, {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Điểm gửi xe',
                                    min: 1,
                                    max: 500
                                }
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {storeId, time, petSex, weight, service, phone, email, typePet} = req.body;
                        if (new Date(time).getTime() < new Date().getTime()) {
                            return ChildRouter.responseError("Thời gian đến spa phải lớn hơn thời gian hiện tại", res);
                        }

                        weight = weight.replace('Kg', '');
                        weight = weight.replace('kg', '');
                        weight = weight.trim();

                        ServicesModel.MODEL.tangLuotDangKy(storeId);
                        let store = await ServicesModel.MODEL.getServicesById(storeId);
                        let book = await BookSpaModel.MODEL.addBookSpa({
                            userId: req.user._id,
                            storeId,
                            storeManagerId: store.userId,
                            timeCheckIn: new Date(time).getTime(),
                            petSex,
                            weight,
                            service,
                            phone, email, typePet
                        });
                        NotificationModel.MODEL.addNewNotification(store.userId, req.user._id, "User đặt dịch vụ", 1, io, {
                            bookId: book._id,
                            typeService: 3,
                            orderId: book.orderId,
                            storeId
                        });
                        return ChildRouter.responseSuccess("Đặt spa thành công", res);
                    }],
                },
            },
            // API v2
            '/booking-v2': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'userId',
                                    type: this.dataType.string,
                                    name: 'UserId',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'phone',
                                    type: this.dataType.string,
                                    name: 'Số điện thoại',
                                    min: 10,
                                    max: 15
                                },
                                {
                                    key: 'items',
                                    type: this.dataType.array,
                                    name: 'Items',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Thương hiệu',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'bookingType',
                                    type: this.dataType.number,
                                    name: 'Đặt dịch vụ',
                                    in: [1, 2, 3],
                                },
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {
                            storeId,
                            time, // timeCheckIn
                            timeCheckOut,
                            weight,
                            items,
                            phone,
                            typePet,
                            note, // nếu là đặt lịch khám: tương ứng triệu chứng cũ.
                            branchName,
                            branchAddress,
                            price,
                            branchPhone,
                            bankCode,
                            orderId,
                            coupon,
                            gCoupon,
                            valueCoupon,
                            bookingType,
                            timeValue, // số ngày hoặc số giờ
                            timeType, // tính tiền theo ngày hoặc giờ: day, hour
                            pickupAddress, //điểm đón
                        } = req.body;

                        let msgText = {
                            0: {
                                SUCCESS: 'SHOP_ORDER_SUCCESS',
                                FAILED: 'SHOP_ORDER_FAILED',
                            },
                            1: {
                                SUCCESS: 'VET_BOOKING_SUCCESS',
                                FAILED: 'VET_BOOKING_FAILED',
                                SYMPTOM_EMPTY: 'VET_SYMPTOM_EMPTY',
                            },
                            2: {
                                SUCCESS: 'ROOM_BOOKING_SUCCESS',
                                FAILED: 'ROOM_BOOKING_FAILED',
                                ROOM_TIME_CHECKOUT_EMPTY: 'ROOM_TIME_CHECKOUT_EMPTY',
                                ROOM_TIME_FAILED: 'ROOM_TIME_FAILED',
                            },
                            3: {
                                SUCCESS: 'SPA_BOOKING_SUCCESS',
                                FAILED: 'SPA_BOOKING_FAILED',
                            },
                            common: {
                                timeFail: 'COMMON_TIME_FAILED',
                                timeValidateFail: 'COMMON_TIME_VALIDATE_FAILED',
                                bookingFail: 'COMMON_BOOKING_FAILED',
                                orderExist: 'COMMON_ORDER_EXIST',
                            }
                        }
                        orderId = orderId ? orderId : new Date().getTime();
                        bookingType = Number(bookingType);
                        // Check exist Order - Fix Duplicate
                        let checkOrderExist = await BookingUtil.checkExistOrder(orderId, bookingType);
                        if (checkOrderExist) {
                            return ChildRouter.responseError(msgText['common'].orderExist, res);
                        }
                        // Lay thong tin store
                        let store = await ServicesModel.MODEL.getServicesById(storeId);

                        // Validate Chung

                        let dataValidateTime = await BookingUtil.beforeBooking(req, items, bookingType)

                        // Todo: can review them
                        // if (dataValidateTime.error) {
                        //     return ChildRouter.responseError(msgText['common'].timeValidateFail, res, {dataValidateTime});
                        // }


                        const userId = req.user._id

                        let paymentMethod = (bankCode && bankCode !== 'TM') ? 1 : 0;
                        // XU LY THANH TOAN
                        // BankCode TM nghĩa là tiền mặt
                        let vnpUrl = '';
                        if (bankCode && bankCode !== 'TM') {
                            vnpUrl = await BookingUtil.vnpayRequestPayment(req, orderId, userId, price, bankCode)
                        }
                        //END XU LY THANH TOAN

                        let rs = null;

                        // * 0: Cửa hàng // đã bỏ
                        // * 1: Phòng Khám
                        // * 2: Điểm gửi xe
                        // * 3: Spa
                        switch (bookingType) {
                            case 1:
                                // 1: Phòng Khám
                                rs = await BookExaminationModel.MODEL.addBookExamination({
                                    orderId,
                                    userId,
                                    storeId,
                                    storeManagerId: store.userId,
                                    phone,
                                    typePet,
                                    note,
                                    branchName,
                                    branchAddress,
                                    branchPhone,
                                    items,
                                    price,
                                    bankCode,
                                    paymentMethod,
                                    coupon,
                                    gCoupon,
                                    pickupAddress
                                });
                                break;
                            case 2:
                                // 2: Điểm gửi xe
                                rs = await BookRoomModel.MODEL.addBookRoom({
                                    orderId,
                                    userId,
                                    storeId,
                                    storeManagerId: store.userId,
                                    items,
                                    phone,
                                    typePet,
                                    note,
                                    branchName,
                                    branchAddress,
                                    price, // total
                                    branchPhone,
                                    bankCode,
                                    paymentMethod,
                                    coupon,
                                    gCoupon,
                                    pickupAddress
                                });
                                break;
                            case 3:
                                // 3: Spa
                                rs = await BookSpaModel.MODEL.addBookSpa({
                                    orderId,
                                    userId,
                                    storeId,
                                    storeManagerId: store.userId,
                                    items,
                                    phone,
                                    typePet,
                                    note,
                                    branchName,
                                    branchAddress,
                                    price,
                                    branchPhone,
                                    bankCode,
                                    paymentMethod,
                                    coupon,
                                    gCoupon,
                                    pickupAddress
                                });
                                break;
                        }

                        if (rs !== null) {
                            // Cap nhat luot booking to brand
                            await ServicesModel.MODEL.tangLuotDangKy(storeId);
                            // Update khi dung coupon
                            coupon = coupon ? coupon : '';
                            gCoupon = gCoupon ? gCoupon : '';
                            await CouponUtil.updateUserCoupon(coupon, userId, orderId, storeId, bookingType)
                            await CouponUtil.updateUserCoupon(gCoupon, userId, orderId, storeId, bookingType)
                            // Gui Thong Bao
                            await NotificationModel.MODEL.addNewNotification(store.userId, userId, "Khách hàng đặt dịch vụ", 1, io, {
                                bookId: rs._id.toString(),
                                typeService: bookingType, // booking spa
                                storeId,
                                orderId
                            });

                            return ChildRouter.responseSuccess(msgText[bookingType].SUCCESS, res, {vnpUrl});
                        } else {
                            return ChildRouter.responseSuccess(msgText['common'].bookingFail, res, {vnpUrl});
                        }

                    }],
                },
            },

            '/booking-show-room': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'userId',
                                    type: this.dataType.string,
                                    name: 'UserId',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'phone',
                                    type: this.dataType.string,
                                    name: 'Số điện thoại',
                                    min: 10,
                                    max: 15
                                },
                                {
                                    key: 'items',
                                    type: this.dataType.array,
                                    name: 'Items',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Thương hiệu',
                                    min: 1,
                                    max: 500
                                },
                            ]
                        }
                    }
                },
                methods: {
                    post: [async function (req, res) {
                        let {
                            storeId,
                            time, // timeCheckIn
                            timeCheckOut,
                            items,
                            phone,
                            note, // nếu là đặt lịch khám: tương ứng triệu chứng cũ.
                            branchName,
                            branchAddress,
                            price,
                            branchPhone,
                            orderId,
                            coupon,
                            gCoupon,
                            pickupAddress, //điểm đón
                        } = req.body;

                        let msgText = {
                            0: {
                                SUCCESS: 'SHOP_ORDER_SUCCESS',
                                FAILED: 'SHOP_ORDER_FAILED',
                            },
                            1: {
                                SUCCESS: 'VET_BOOKING_SUCCESS',
                                FAILED: 'VET_BOOKING_FAILED',
                                SYMPTOM_EMPTY: 'VET_SYMPTOM_EMPTY',
                            },
                            2: {
                                SUCCESS: 'ROOM_BOOKING_SUCCESS',
                                FAILED: 'ROOM_BOOKING_FAILED',
                                ROOM_TIME_CHECKOUT_EMPTY: 'ROOM_TIME_CHECKOUT_EMPTY',
                                ROOM_TIME_FAILED: 'ROOM_TIME_FAILED',
                            },
                            3: {
                                SUCCESS: 'SPA_BOOKING_SUCCESS',
                                FAILED: 'SPA_BOOKING_FAILED',
                            },
                            common: {
                                timeFail: 'COMMON_TIME_FAILED',
                                timeValidateFail: 'COMMON_TIME_VALIDATE_FAILED',
                                bookingFail: 'COMMON_BOOKING_FAILED',
                                orderExist: 'COMMON_ORDER_EXIST',
                            }
                        }
                        orderId = orderId ? orderId : new Date().getTime();
                        // Check exist Order - Fix Duplicate
                        let checkOrderExist = await BookingUtil.checkExistOrder(orderId, TypeServices.SHOWROOM);
                        if (checkOrderExist) {
                            return ChildRouter.responseError(msgText['common'].orderExist, res);
                        }
                        // Lay thong tin store
                        let store = await ServicesModel.MODEL.getServicesById(storeId);

                        // Validate Chung

                        const userId = req.user._id

                        let rs = null;

                        rs = await BookClassificationModel.MODEL.create({
                            orderId,
                            userId,
                            storeId,
                            storeManagerId: store.userId,
                            items,
                            phone,
                            note,
                            branchName,
                            branchAddress,
                            price,
                            branchPhone,
                            pickupAddress
                        });

                        if (rs !== null) {
                            // Cap nhat luot booking to brand
                            await ServicesModel.MODEL.tangLuotDangKy(storeId);
                            // Gui Thong Bao
                            await NotificationModel.MODEL.addNewNotification(store.userId, userId, "Đặt bàn", 1, io, {
                                bookId: rs._id.toString(),
                                typeService: TypeServices.SHOWROOM,
                                storeId,
                                orderId
                            });

                            return ChildRouter.responseSuccess("Đặt bàn thành công", res, rs);
                        } else {
                            return ChildRouter.responseSuccess(msgText['common'].bookingFail, res, {});
                        }

                    }],
                },
            },

            '/booking-hotel-calculator.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'roomId',
                                    type: this.dataType.string,
                                    name: 'Mã',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'classifyId',
                                    type: this.dataType.string,
                                    name: 'Thuộc tính',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'classifySubId',
                                    type: this.dataType.string,
                                    name: 'Dữ liệu',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'weight',
                                    type: this.dataType.string,
                                    name: 'Cân nặng',
                                    min: 1,
                                    max: 10
                                },
                                {
                                    key: 'time',
                                    type: this.dataType.string,
                                    name: 'Thời gian nhận phòng',
                                    min: 5,
                                    max: 100
                                },
                                {
                                    key: 'timeCheckOut',
                                    type: this.dataType.string,
                                    name: 'Thời gian trả phòng',
                                    min: 5,
                                    max: 100
                                },
                                {
                                    key: 'storeId',
                                    type: this.dataType.string,
                                    name: 'Thương hiệu',
                                    min: 1,
                                    max: 500
                                },
                            ]
                        }
                    }

                },
                methods: {
                    post: [async function (req, res) {
                        let {
                            storeId,
                            time, // timeCheckIn
                            timeCheckOut,
                            weight,
                            classifyId,
                            classifySubId,
                            roomId,
                            code,
                        } = req.body;

                        // Validate
                        if (new Date(time).getTime() < new Date().getTime()) {
                            return ChildRouter.responseError('COMMON_TIME_FAILED', res);
                        }
                        if (!timeCheckOut) {
                            return ChildRouter.responseError('ROOM_TIME_CHECKOUT_EMPTY', res);
                        }
                        if (new Date(time).getTime() > new Date(timeCheckOut).getTime()) {
                            return ChildRouter.responseError('ROOM_TIME_FAILED', res);
                        }

                        let discount = 0
                        let rsCoupon = {
                            msg: '',
                            error: false
                        }
                        let data = null
                        code = code ? code.toLowerCase() : code;
                        let coupon = await CouponModel.MODEL.getDataWhere({
                            code,
                            status: 1
                        }, CouponModel.MODEL.FIND_ONE(), {createAt: -1});

                        if (coupon) {
                            if (coupon.typeCode == 1 && coupon.storeId != storeId) {
                                rsCoupon = {
                                    msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                                    error: true
                                }
                            }
                            let currentTime = new Date().getTime();
                            if (Number(coupon.currentBooking) < Number(coupon.countBooking)
                                && currentTime >= coupon.startTime && currentTime <= coupon.endTime) {

                                // Kiểm tra coupon limit by day.
                                let userId = req.user._id;

                                let getCouponUser = await CouponUserModel.MODEL.getDataWhere({
                                    coupon: coupon.code,
                                    userId
                                }, CouponUserModel.MODEL.FIND_ONE(), {createAt: -1});

                                if (getCouponUser) {
                                    let createAt = Number(getCouponUser.createAt);
                                    let timeOneDay = 1 * 24 * 60 * 60 * 1000; // Cố định 1 day : tính theo Timestamp milliseconds
                                    if ((currentTime - createAt) < timeOneDay) {
                                        rsCoupon = {
                                            msg: 'Bạn đã dùng mã này trong 24 giờ trước, vui lòng sử dụng sau.',
                                            error: true
                                        }
                                    }
                                }
                                // // GIAM GIA THEO % type = 1
                                // if (coupon)
                                // {
                                //     discount = coupon.type === 1 ? (subTotal * Number(coupon.value) || 0) / 100 : Number(coupon.value)
                                // }

                            } else {
                                rsCoupon = {
                                    msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                                    error: true
                                }
                            }
                        } else if (code) {
                            rsCoupon = {
                                msg: 'Mã giảm giá không hợp lệ hoặc đã hết hạn',
                                error: true
                            }
                        }

                        const userId = req.user._id

                        let store = await ServicesModel.MODEL.getServicesById(storeId);
                        let room = await RoomOfHotelModel.MODEL.getRoomById(roomId);
                        if (room) {
                            let roomClassify = room.classify.map(x => {
                                return {id: x._id.toString(), ...x}
                            });

                            let price = room.price;
                            if (roomClassify) {
                                let pickedClassify = lodash.filter(roomClassify, {"id": classifyId});

                                if (pickedClassify && pickedClassify[0] != null) {
                                    let roomSubClassify = pickedClassify[0].data.map(x => {
                                        return {id: x._id.toString(), ...x}
                                    });
                                    let pickedSub = lodash.filter(roomSubClassify, {"id": classifySubId});

                                    price = pickedSub && pickedSub[0] ? pickedSub[0].price : null;
                                    price = price ? price.replace(/[^0-9\.]+/g, "") : 0;
                                    // console.log(price)
                                }
                            }
                            // Tinh ngay.
                            let timeValue = 0;
                            let timeType = room.timeType ? room.timeType : 'day';
                            let timeCI = moment(time);
                            let timeCO = moment(timeCheckOut);
                            // console.log('Time CheckIn', timeCI);
                            // console.log('Time Check Out', timeCO);

                            let betweenTime = !store.betweenTime ? '12:00:00' : store.betweenTime;
                            let duration = moment.duration(timeCO.diff(timeCI));
                            // console.log('betweenTime', betweenTime)

                            // timeType = 'day'; // Test fixed : Hour

                            if (timeType === 'hour') {
                                timeValue = duration.asHours();
                            } else {
                                const days = duration.asDays();
                                timeValue = Math.ceil(days);
                                // So sánh thời gian
                                const dateTimeCO = timeCO.format("HH:mm:ss")
                                // console.log('dateTimeCO', dateTimeCO)
                                if (dateTimeCO > betweenTime) {
                                    // timeValue += 0.5; // cộng số ngày thêm 0.5 // Todo Tinh Ngay tạm thời bỏ + 0.5 ngày.
                                }
                            }

                            const subTotal = timeValue * price;
                            if (coupon && !rsCoupon.error) {
                                // GIAM GIA THEO % type = 1
                                discount = coupon.type === 1 ? (subTotal * Number(coupon.value) || 0) / 100 : Number(coupon.value)
                            }
                            const totalPrice = subTotal - (discount > 50000 ? 50000 : discount) //TODO: add rules
                            let result = {
                                timeType: timeType,
                                timeValue: timeValue,
                                price,
                                subTotal,
                                discount,
                                totalPrice,
                                coupon,
                                rsCoupon
                            }
                            return ChildRouter.responseSuccess("Thành công", res, result);
                        }
                        return ChildRouter.responseError('Thông tin phòng không đúng.', res);
                    }],
                },
            },

            // END API BOOKING

            '/tai-thong-tin-gio-hang.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',

                },

                methods: {
                    post: [async function (req, res) {
                        let shopProducts = req.body.shopProducts || [];
                        let removeProducts = [];
                        console.log(shopProducts)
                        let mapShopFunc = shopProducts.map((shopData, iS) => {
                            return new promise(async resolve => {
                                let shopId;
                                let products;
                                for (let shopIdIn in shopData) {
                                    shopId = shopIdIn;
                                    products = shopData[shopIdIn].products;
                                }

                                shopProducts[iS][shopId].info = await ServicesModel.MODEL.getServicesById(shopId);
                                if (shopProducts[iS][shopId].info) {
                                    let mapProductFunc = products.map((p, iP) => {
                                            return new promise(async resolve => {
                                                let realPId = getProductId(p.productId)
                                                let pInfo = await ProducModel.MODEL.getProductsById(realPId);
                                                if (pInfo) {
                                                    shopProducts[iS][shopId].products[iP].info = pInfo;
                                                } else {
                                                    removeProducts.push(p.productId);
                                                }
                                                return resolve();
                                            })
                                        }
                                    );

                                    await promise.all(mapProductFunc);
                                } else {
                                    products.forEach(p => {
                                        removeProducts.push(p.productId);
                                    });
                                }

                                return resolve();
                            })
                        });


                        await promise.all(mapShopFunc);
                        let newData = [];
                        shopProducts.forEach((e) => {
                            let shopId;
                            for (let shopIdIn in e) {
                                shopId = shopIdIn;
                            }

                            if (e[shopId].info) {
                                let products = e[shopId].products;
                                let newP = [];
                                products.forEach((p) => {
                                    if (p.info) {
                                        let classifies = [];
                                        if (p.classifyActive) {
                                            let index = 0;
                                            for (let parentId in p.classifyActive) {
                                                // let name = p.info.classify[index].name;
                                                let name = "Kích Thước";
                                                classifies.push({
                                                    index: index,
                                                    name,
                                                    value: p.classifyActive[parentId],
                                                    price: p.classifyActive.price
                                                });
                                                index++;
                                            }
                                        }
                                        p.classifies = classifies;
                                        newP.push(p);
                                    }
                                });
                                e[shopId].products = newP;
                                e.info = e[shopId].info;
                                delete e[shopId].info;
                                newData.push(e);
                            }
                        });
                        return ChildRouter.responseSuccess("thành công", res, {shopProducts: newData, removeProducts});
                    }],
                },
            },

            '/create-order-product': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'userId',
                                    type: this.dataType.string,
                                    name: 'Người dùng',
                                    min: 1,
                                    max: 500
                                },
                                {
                                    key: 'phone',
                                    type: this.dataType.string,
                                    name: 'Số điện thoại',
                                    min: 10,
                                    max: 15
                                },
                                {
                                    key: 'province',
                                    type: this.dataType.string,
                                    name: 'Tỉnh/Thành phố',
                                    min: 1,
                                    max: 200
                                },
                                {
                                    key: 'district',
                                    type: this.dataType.string,
                                    name: 'Quận huyện',
                                    min: 1,
                                    max: 200
                                },
                                {
                                    key: 'ward',
                                    type: this.dataType.string,
                                    name: 'Phường/Xã',
                                    min: 1,
                                    max: 200
                                },
                                {
                                    key: 'street',
                                    type: this.dataType.string,
                                    name: 'Đường',
                                    min: 1,
                                    max: 200
                                },
                                {
                                    key: 'name',
                                    type: this.dataType.string,
                                    name: 'Họ và tên',
                                    min: 1,
                                    max: 200
                                },
                                {
                                    key: 'totalAmount',
                                    type: this.dataType.string,
                                    name: 'Tổng giá',
                                    min: 1,
                                    max: 50
                                },
                                {
                                    key: 'items',
                                    type: this.dataType.array,
                                    name: 'Sản phẩm',
                                    min: 1,
                                    max: 500
                                },
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        const userIdReq = req.user._id;
                        let {
                            userId,
                            name,
                            phone,
                            items,
                            note,
                            province,
                            district,
                            ward,
                            street,
                            location,
                            paymentMethod,
                            coupon,
                            bankCode,
                            paymentId,
                            totalAmount,
                            transportFee
                        } = req.body;
                        let couponG = coupon
                        if (userId !== userIdReq) {
                            return ChildRouter.responseError("INVALID_USER", res);
                        }

                        let bookingType = 0 // Type order product.
                        items = req.body.items || [];
                        phone = phone || '';
                        note = note || '';
                        couponG = couponG || '';
                        paymentMethod = paymentMethod || 0; // 0 : Tiền mặt / 1: Thanh Toán Online / 2 Dùng Điểm
                        paymentId = paymentId ? paymentId : new Date().getTime();
                        bankCode = bankCode || '';
                        totalAmount = Number(totalAmount);
                        transportFee = Number(transportFee);

                        // Tổng tiền + phí ship
                        totalAmount += transportFee;

                        let vnpUrl = '';

                        if (bankCode && bankCode !== 'TM') {
                            vnpUrl = await BookingUtil.vnpayRequestPayment(req, paymentId, req.user._id, totalAmount, bankCode, items)
                        }

                        // Validate data product
                        const validate = await ValidateUtil.validateProductOrder(items);
                        if (validate.error) {
                            return ChildRouter.responseError("VALIDATE_PRODUCT_ORDER_ERROR", res, validate);
                        }

                        // Tạo từng order theo từng shop

                        for (const itemData of items) {

                            if (itemData) {
                                let storeId = itemData.storeId;
                                let products = itemData.products;
                                let couponShop = itemData.rsCoupon ? itemData.rsCoupon : '';
                                let transportFee = itemData.transportFee;
                                let shippingService = itemData.shippingService;

                                let totalWeight = itemData.totalWeight;
                                let price = 0;

                                // check truong hop co gia con

                                products.forEach(p => {
                                    const realPId = getProductId(p.productId)
                                    p.productId = realPId
                                    if (p.classifyActive) {
                                        let priceAddClassify = p.classifyActive.price;
                                        let priceAdd = getNumberPrice(priceAddClassify)
                                        price += priceAdd * p.count
                                    } else {
                                        let priceAddClassify = p.price;
                                        price += priceAddClassify * p.count
                                    }
                                    ProducModel.MODEL.addCountRevenueProduct(realPId, p.count);
                                });

                                let totalPriceShop = itemData.totalPriceShop ? itemData.totalPriceShop : price;

                                let shopInfo = await ServicesModel.MODEL.getServicesById(storeId);

                                if (couponG && couponShop) {
                                    const rsDiscount = await BookingUtil.calculateCouponShop(userId, couponG, price, transportFee)
                                    if (rsDiscount) {
                                        totalPriceShop -= rsDiscount.discount
                                        await CouponModel.MODEL.addCurrentBookingCount(rsDiscount.coupon._id) // nếu mã dùng 1 lần chuyển mã về hết hạn
                                    }
                                    // giam gia theo ma giam gia chung neu co coupon
                                } else if (couponG && !couponShop) {
                                    const rsDiscount = await BookingUtil.calculateCouponShop(userId, couponG, totalPriceShop, transportFee)
                                    if (rsDiscount) {
                                        totalPriceShop -= rsDiscount.discount
                                        await CouponModel.MODEL.addCurrentBookingCount(rsDiscount.coupon._id)  // nếu mã dùng 1 lần chuyển mã về hết hạn
                                    }
                                }

                                let storeUserId = shopInfo.userId;
                                let address = (street || '') + ', ' + (ward || '') + ', ' + (district || '') + ', ' + (province || '');
                                const orderId = new Date().getTime();
                                let obj = {
                                    orderId,
                                    paymentId,
                                    userBuyFullName: name,
                                    userId: req.user._id,
                                    storeId,
                                    storeUserId,
                                    products,
                                    phone,
                                    note,
                                    province,
                                    district,
                                    ward,
                                    street,
                                    location,
                                    address,
                                    paymentMethod,
                                    bankCode,
                                    coupon: couponShop,
                                    shippingService,
                                    transportFee,
                                    totalWeight,
                                    totalPriceShop
                                }

                                let request = await RequestBuyProductModel.MODEL.addRequest(obj);
                                if (request) {
                                    if (couponShop) {
                                        await CouponUtil.updateUserCoupon(couponShop, userId, orderId, storeId, bookingType)
                                    }
                                    await NotificationModel.MODEL.addNewNotification(storeUserId, req.user._id, "Người dùng đã đặt hàng", 0, io, {
                                        requestId: request._id,
                                        storeId: storeId,
                                        orderId,
                                        typeService: TypeServices.SHOP
                                    });
                                }
                            }
                        }

                        if (couponG) {
                            await CouponUtil.updateUserCoupon(couponG, userId, paymentId, '', bookingType)
                        }


                        return ChildRouter.responseSuccess("ORDER_SUCCESS", res, {vnpUrl, paymentId});
                    }],
                },
            },
            '/create-url-payment': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'totalAmount',
                                    type: this.dataType.string,
                                    name: 'Tổng giá',
                                    min: 1,
                                    max: 50,
                                },
                                {
                                    key: 'bankCode',
                                    type: this.dataType.string,
                                    name: 'Mã ngân hàng',
                                    min: 1,
                                    max: 50,
                                },
                            ]
                        }
                    }
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            bankCode,
                            paymentId,
                            totalAmount,
                            data
                        } = req.body;

                        paymentId = paymentId ? paymentId : new Date().getTime();
                        bankCode = bankCode || '';
                        totalAmount = Number(totalAmount);

                        let vnpUrl = '';

                        if (bankCode && bankCode !== 'TM') {
                            vnpUrl = await BookingUtil.vnpayRequestPayment(req, paymentId, req.user._id, totalAmount, bankCode, [])
                        }

                        return ChildRouter.responseSuccess("CREATE_URL_SUCCESS", res, {vnpUrl, paymentId, data});
                    }],
                },
            },

            '/get-ads.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',

                },

                methods: {
                    get: [async function (req, res) {
                        let ads = await AdsSettingModel.MODEL.getSettingAds();
                        return ChildRouter.renderOrResponse(req, res, {ads});
                    }],
                },
            },

            '/get-banner.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',

                },

                methods: {
                    get: [async function (req, res) {
                        let condition = {
                            status: 0
                        }
                        if (req.headers['province']) {
                            let province = req.headers['province']
                            condition['$or'] = [
                                {province: {'$regex': StringUtils.removeUtf8(province)}},
                                {province: {'$regex': 'toan-quoc'}},
                            ]
                            // condition['province'] = {'$regex': StringUtils.removeUtf8(province)}
                        }

                        let banners = await BannerAppModel.MODEL.getBannerByCondition(condition, null, {order: 1})
                        return ChildRouter.responseSuccess('Thanh Cong', res, {banners});
                    }],
                },
            },
            '/get-banner-store.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',

                },

                methods: {
                    get: [async function (req, res) {
                        let {tag} = req.query;
                        let banners = [];
                        let bannersData;

                        let condition = {
                            status: 0
                        }

                        if (req.headers['province']) {
                            let province = req.headers['province']
                            condition['$or'] = [
                                {province: {'$regex': StringUtils.removeUtf8(province)}},
                                {province: {'$regex': 'toan-quoc'}},
                            ]
                            // condition['province'] = {'$regex': StringUtils.removeUtf8(province)}
                        }

                        if (tag) {
                            condition.tag = tag;
                            bannersData = await AdsStoreModel.MODEL.getBannerByCondition(condition, null, {
                                order: 1
                            })

                        } else {
                            bannersData = await AdsStoreModel.MODEL.getAllBanners()
                        }

                        for (let banner of bannersData) {
                            if (banner.screen == 'SERVICE_DETAIL' && banner.params.length == 24) {
                                let storeInfo = await ServicesModel.MODEL.getServiceWithBranchById(banner.params)
                                if (storeInfo) {
                                    banner.storeInfo = storeInfo;
                                }
                            }
                            banners.push(banner);
                        }

                        return ChildRouter.responseSuccess('Thanh Cong', res, {banners});
                    }],
                },
            },
            '/lay-lich-su-kham-benh/:type': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        if (Number(req.params.type) == 0) {
                            let ganDay = await BookExaminationModel.MODEL.getBookExaminationForUser({
                                userId: req.user._id,
                                timeCheckIn: {$gte: new Date().getTime()}
                            }, {timeCheckIn: 1, createAt: -1});
                            let quaHan = await BookExaminationModel.MODEL.getBookExaminationForUser({
                                userId: req.user._id,
                                timeCheckIn: {$lt: new Date().getTime()}
                            }, {timeCheckIn: 1, createAt: -1});
                            return ChildRouter.responseSuccess("Thành công", res, {ganDay, quaHan});
                        } else {
                            let lichsu = await BookExaminationModel.MODEL.getBookExaminationForUser({
                                userId: req.user._id,
                            }, {createAt: -1});
                            return ChildRouter.responseSuccess("Thành công", res, {lichsu});
                        }
                    }],
                },
            },
            '/lay-lich-su-khach-san/:type': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        if (Number(req.params.type) == 0) {
                            let ganDay = await BookRoomModel.MODEL.getBookRoomForUser({
                                userId: req.user._id,
                                timeCheckIn: {$gte: new Date().getTime()}
                            }, {timeCheckIn: 1, createAt: -1});
                            let quaHan = await BookRoomModel.MODEL.getBookRoomForUser({
                                userId: req.user._id,
                                timeCheckIn: {$lt: new Date().getTime()}
                            }, {timeCheckIn: 1, createAt: -1});
                            return ChildRouter.responseSuccess("Thành công", res, {ganDay, quaHan});
                        } else {
                            let lichsu = await BookRoomModel.MODEL.getBookRoomForUser({
                                userId: req.user._id,
                            }, {createAt: -1});
                            return ChildRouter.responseSuccess("Thành công", res, {lichsu});
                        }

                    }],
                },
            },

            '/lay-lich-su-spa.html/:type': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, limit} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        limit = Number(limit)
                        if (Number(req.params.type) == 0) {
                            let ganDay = await BookSpaModel.MODEL.getBookSpaForUser({
                                userId: req.user._id,
                                timeCheckIn: {$gte: new Date().getTime()}
                            }, {timeCheckIn: 1, createAt: -1});
                            let quaHan = await BookSpaModel.MODEL.getBookSpaForUser({
                                userId: req.user._id,
                                timeCheckIn: {$lt: new Date().getTime()}
                            }, {timeCheckIn: 1, createAt: -1});
                            return ChildRouter.responseSuccess("Thành công", res, {ganDay, quaHan});
                        } else {
                            let lichsu = await BookSpaModel.MODEL.getBookSpaForUser({
                                userId: req.user._id,
                            }, {createAt: -1}, page, limit);

                            let totalPage = await BookSpaModel.MODEL.getTotalPage({
                                userId: req.user._id,
                            }, limit);
                            return ChildRouter.responseSuccess("Thành công", res, {lichsu, totalPage});
                        }
                    }],
                },
            },

            '/get-list-booking-show-room/:type': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {page, limit} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        limit = Number(limit)
                        let lichsu = await BookClassificationModel.MODEL.getDataForUser({
                            userId: req.user._id,
                        }, {createAt: -1}, page, limit);

                        let totalPage = await BookClassificationModel.MODEL.getTotalPage({
                            userId: req.user._id,
                        }, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {lichsu, totalPage});
                    }],
                },
            },

            '/order-booking-spa-detail/:orderId': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let booking = await BookSpaModel.MODEL.getOneBookSpaByCondition({
                            userId: req.user._id,
                            orderId: req.params.orderId
                        })
                        return ChildRouter.responseSuccess("Thành công", res, {detail: booking});
                    }],
                },
            },

            '/order-booking-show-room-detail/:orderId': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let booking = await BookClassificationModel.MODEL.getOneByCondition({
                            userId: req.user._id,
                            orderId: req.params.orderId
                        })
                        return ChildRouter.responseSuccess("Thành công", res, {detail: booking});
                    }],
                },
            },

            '/order-booking-hotel-detail/:orderId': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let booking = await BookRoomModel.MODEL.getOneBookRoomByCondition({
                            userId: req.user._id,
                            orderId: req.params.orderId
                        })
                        return ChildRouter.responseSuccess("Thành công", res, {detail: booking});
                    }],
                },
            },

            '/order-booking-clinic-detail/:orderId': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let booking = await BookExaminationModel.MODEL.getOneBookExaminationByCondition({
                            userId: req.user._id,
                            orderId: req.params.orderId
                        })
                        return ChildRouter.responseSuccess("Thành công", res, {detail: booking});
                    }],
                },
            },
            '/order-product-detail/:orderId': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let booking = await RequestBuyProductModel.MODEL.getOrderDetail({
                            userId: req.user._id,
                            orderId: req.params.orderId
                        })
                        return ChildRouter.responseSuccess("Thành công", res, {detail: booking});
                    }],
                },
            },
            '/lay-lich-su-mua-hang': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {page, limit} = req.query;
                        if (!page) page = 1;
                        if (!limit) limit = 10;
                        page = Number(page);
                        limit = Number(limit)

                        let userId = req.user._id;
                        let lichsu = await RequestBuyProductModel.MODEL.getRequestForUser({
                            userId
                        }, {createAt: -1}, page, limit);
                        let totalPage = await RequestBuyProductModel.MODEL.getTotalPage({
                            userId: req.user._id,
                        }, limit);

                        return ChildRouter.responseSuccess("Thành công", res, {lichsu, totalPage});
                    }],
                },
            },
            '/lich-sua-mua-hang-thanh-cong': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {page, limit, timeStart, timeEnd, orderAmount} = req.query;
                        if (!page) page = 1;
                        if (!limit) limit = 10;
                        page = Number(page);
                        limit = Number(limit)

                        let userId = req.user._id;
                        let muahang = await RequestBuyProductModel.MODEL.getRequestForUser({
                            userId,
                            createAt: {
                                $gte: new Date(timeStart).getTime() - 60000,
                                $lte: new Date(timeEnd).getTime() + 60000
                            },
                            totalPriceShop: {
                                $gte: Number(orderAmount || 0)
                            }
                        }, {createAt: -1}, page, limit);

                        return ChildRouter.responseSuccess("Thành công", res, { muahang });
                    }],
                },
            },
            '/user-point-history': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {page, limit} = req.query;
                        if (!page) page = 1;
                        if (!limit) limit = 10;
                        page = Number(page);
                        limit = Number(limit)

                        let userId = req.user._id;
                        let data = await UserPointLogModel.MODEL.getByCondition({
                            userId
                        });

                        return ChildRouter.responseSuccess("Thành công", res, {records: data});
                    }],
                },
            },

            '/tim-kiem-phong-kham.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, page} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        let limit = 10;

                        let result = await ServicesModel.MODEL.getServicesByConditionWithPage({
                            type: 1, status: 1, $or: [
                                {'nameUTF': {'$regex': StringUtils.removeUtf8(search)}},
                                {'addressUTF': {'$regex': StringUtils.removeUtf8(search)}},
                            ]
                        }, page, {luotDangKy: -1, createAt: -1}, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {result});
                    }],
                },
            },

            '/tim-kiem-khach-san.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, page} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        let limit = 10;
                        let result = await ServicesModel.MODEL.getServicesByConditionWithPage({
                            type: 2, status: 1, $or: [
                                {'nameUTF': {'$regex': StringUtils.removeUtf8(search)}},
                                {'addressUTF': {'$regex': StringUtils.removeUtf8(search)}},
                            ]
                        }, page, {luotDangKy: -1, createAt: -1}, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {result});
                    }],
                },
            },

            '/tim-kiem-spa.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, page} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        let limit = 10;

                        let conditionOr = [
                            {'nameUTF': {'$regex': StringUtils.removeUtf8(search)}},
                            {'addressUTF': {'$regex': StringUtils.removeUtf8(search)}},
                            {'provinceUTF': {'$regex': StringUtils.removeUtf8(search)}}]

                        let totalPage = await ServicesModel.MODEL.getTotalPage({
                            type: 3, status: 1, $or: conditionOr
                        });
                        let result = await ServicesModel.MODEL.getServicesByConditionWithPage({
                            type: 3, status: 1, $or: conditionOr
                        }, page, {luotDangKy: -1, createAt: -1}, limit);
                        return ChildRouter.responseSuccess("Thành công", res, {result, totalPage});
                    }],
                },
            },

            '/search.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, page, type, distance, limit, categoryId, sort} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        limit = Number(limit) || 10;

                        let data = null;
                        // Search For Product
                        if (type == 0) {
                            const conditionOr = ['nameUTF', 'descriptionUTF', 'addressUTF'].map(key => ({
                                [key]: {
                                    $regex: StringUtils.removeUtf8(search)
                                }
                            }));
                            let condition = {
                                status: 1, $or: conditionOr
                            };
                            if (categoryId) condition.categoryId = categoryId;
                            data = await ProducModel.MODEL.getProductByConditionWithPageLookup(condition, page, limit, conditionOr);
                        } else {
                            distance = distance ? (Number(distance) * 1000) : 1000 * 100
                            const conditionOr = ['nameUTF', 'descriptionUTF', 'addressUTF', 'provinceUTF', 'tags'].map(key => ({
                                [key]: {
                                    $regex: StringUtils.removeUtf8(search)
                                }
                            }));

                            let condition = {
                                status: 1, $or: conditionOr
                            };

                            /**
                             * -1: search all type
                             * 0: Cửa hàng
                             * 1: Phòng Khám
                             * 2: Điểm gửi xe
                             * 3: Spa
                             */

                            if (type != -1) {
                                condition.businessTypes = {
                                    $regex: type
                                }
                            }

                            if (req.headers['province']) {
                                let province = req.headers['province']
                                condition.provinceUTF = {$regex: StringUtils.removeUtf8(province)}
                            }

                            if (req.headers['location']) {
                                const location = JSON.parse(req.headers['location'])
                                const branches = await BranchModel.MODEL.getBranchByLocation(location, distance);
                                const storesId = lodash.uniq(branches.map(x => x.storeId));
                                condition.serviceId = {
                                    $in: storesId
                                };
                            }
                            let obSort = {
                                createAt: -1
                            };
                            if (sort === 'rating') {
                                obSort = {
                                    totalRate: -1,
                                }
                            }
                            data = await ServicesModel.MODEL.getServicesByConditionWithPageLookup(condition, page, limit, obSort);
                        }

                        return ChildRouter.responseSuccess("Thành công", res, {...data});
                    }],
                },
            },

            '/search-v2': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {search, page, type, distance, limit, categoryId, sort} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        limit = Number(limit) || 10;
                        distance = Number(distance || '2000') * 1000

                        let data = null;
                        // Search For Product
                        if (type == TypeServices.SHOP) {
                            let {
                                rate,
                                price,
                                shipping,
                                order,
                                orderby,
                            } = req.query;

                            rate = Number(rate) || 0;
                            order = order || 'sales';
                            orderby = Number(orderby) || -1;

                            const conditionOr = ['nameUTF', 'addressUTF'].map(key => ({
                                [key]: {
                                    $regex: StringUtils.removeUtf8(search)
                                }
                            }));
                            let condition = {
                                status: 1, $or: conditionOr
                            };
                            if (categoryId) condition.categoryId = categoryId;

                            if (price) {
                                price = price.split("-");
                                let minPrice = price[0] ? Number(price[0]) : 0;
                                let maxPrice = price[1] ? Number(price[1]) : 0;
                                condition.price = {}
                                if (minPrice) {
                                    condition.price.$gte = minPrice;
                                }
                                if (maxPrice) {
                                    condition.price.$lte = maxPrice;
                                }
                            }
                            if (shipping) {
                                condition.transport = {
                                    $regex: shipping,
                                    $options: 'i'
                                };
                            }
                            if (rate) {
                                condition.rate = {
                                    $gte: rate
                                }
                            }

                            if (req.query?.typeShip !== undefined) {
                                condition.typeShip = {
                                    $eq: Number(req.query.typeShip)
                                }
                            }

                            let keySort = '';

                            if (order) {
                                switch (order) {
                                    case 'sales':
                                        keySort = 'revenue';
                                        break;
                                    case 'price-asc':
                                        keySort = 'price';
                                        orderby = 1;
                                        break;
                                    case 'price-desc':
                                        keySort = 'price';
                                        orderby = -1;
                                        break;
                                    case 'newest':
                                        // keySort = 'createAt';
                                        keySort = 'code';
                                        orderby = -1;
                                        break;
                                    default:
                                        keySort = order
                                        break;
                                }
                            }
                            let obSort = {
                                [keySort]: orderby
                            }

                            data = await ProducModel.MODEL.getProductByConditionWithPageLookup(condition, page, limit, conditionOr, obSort);
                        } else {
                            let location = null;
                            const conditionOr = ['nameUTF', 'descriptionUTF', 'addressUTF', 'provinceUTF', 'districtUTF', 'tags'].map(key => ({
                                [key]: {
                                    $regex: StringUtils.removeUtf8(search)
                                }
                            }));

                            let condition = {
                                status: 1,
                                $or: conditionOr
                            };

                            /**
                             * -1: search all type
                             * 0: Cửa hàng
                             * 1: Phòng Khám
                             * 2: Điểm gửi xe
                             * 3: Spa
                             * 4: showRoom
                             * 5: Trặm xăng
                             */

                            if (type != -1) {
                                condition.businessTypes = {
                                    $regex: type
                                }
                            }

                            if (req.headers['province']) {
                                let province = req.headers['province']
                                province = province.replace('hcm', 'hồ chí minh')
                                condition.provinceUTF = {$regex: StringUtils.removeUtf8(province)}
                            }

                            if (req.headers['district']) {
                                let district = req.headers['district']
                                condition.districtUTF = {$regex: StringUtils.removeUtf8(district)}
                            }
                            let obSort = {
                                'dist.calculated': 1
                            };
                            if (sort == 'rating') {
                                obSort = {
                                    totalRate: -1,
                                }
                            }
                            let listService = await ServicesModel.MODEL.getServicesByCondition(condition);

                            let listServiceIds = lodash.uniq(listService.map(x => x._id.toString()));

                            let conditionBranch = {
                                storeId: {
                                    $in: listServiceIds
                                }
                            };
                            if (req.headers['location']) {
                                location = JSON.parse(req.headers['location'])
                            }
                            data = await BranchModel.MODEL.searchBranchByLocation(type, conditionBranch, location, distance, obSort, page, limit);
                        }
                        return ChildRouter.responseSuccess("Thành công", res, {...data});
                    }],
                },
            },
            '/favorites': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {page, limit} = req.query;
                        if (!page) page = 1;
                        page = Number(page);
                        limit = Number(limit) || 10;

                        let data = null;
                        let location = null;

                        let where = {
                            usersFavorites: {"$in": [req.user._id]}
                        }

                        data = await DataSearchModel.MODEL.getAllByConditionWithPage(where, {createAt: -1}, page, limit);

                        let totalPage = await DataSearchModel.MODEL.getTotalPage(where, limit)

                        return ChildRouter.responseSuccess("Thành công", res, {
                            result: data,
                            total: data.length,
                            ...totalPage
                        });
                    }],
                },
            },

            '/reset-count-message.html': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await UserModel.MODEL.updateUser(req.user._id, {countMessage: 0});
                        let user = req.user;
                        user.countMessage = 0;
                        UserSession.saveUser(req.session, user);
                        if (req.version !== 'api') {
                            UserSession.saveUser(req.session, user);
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/reset-count-notification.html': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await UserModel.MODEL.resetNotification(req.user._id);
                        let user = req.user;
                        user.notifications = 0;
                        UserSession.saveUser(req.session, user);
                        if (req.version !== 'api') {
                            UserSession.saveUser(req.session, user);
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/xem-tin-nhan.html/:userId': {
                config: {
                    auth: [this.roles.account],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        await MessageModel.MODEL.updateWhereClause({
                                userSendId: req.params.userId,
                                userId: req.user._id, watched: 0
                            },
                            {
                                watched: 1
                            });
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/pay-product-with-point': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let userId = req.user._id;
                        let {productId, shippingInfo} = req.body;

                        let rs;
                        try {
                            rs = await axios.get(`${apiUrlV2}/api/shops/${productId}?populate=*`, {
                                headers: {
                                    "Content-Type": 'application/json'
                                }
                            });
                        } catch (error) {
                            console.log(`Error fetching shop data for product ${productId}:`, error.message);
                            return res.status(500).send({ success: false, msg: 'Không thể lấy thông tin cửa hàng' });
                        }

                        if (rs.status == 200) {
                            const productData = rs.data.data.attributes
                            const productPoint = productData.point;

                            let userData = await UserModel.MODEL.getUsersById(userId);

                            if (userData) {
                                const currentPoint = !userData.point ? 0 : userData.point;
                                if (currentPoint >= productPoint) {
                                    const minusPoint = -productPoint
                                    // trường hợp này orderId chính là ID của sản phẩm chi tiết
                                    await PointUtil.UpdateUserPoint(userId, '', minusPoint, -1, `Đổi điểm thưởng lấy sản phẩm`, productData, shippingInfo); // Cập nhật User Point
                                } else {
                                    return ChildRouter.responseError(`Bạn không đủ ${productPoint} điểm để mua sản phẩm này`, res);
                                }
                            } else {
                                return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
                            }

                            return ChildRouter.responseSuccess("Thành công", res);
                        } else {
                            return ChildRouter.responseError('Có lỗi xảy ra', res);
                        }

                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },
            '/huy-don-hang.html/:id': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let message = req.body.message;

                        let request = await RequestBuyProductModel.MODEL.getRequestById(req.params.id);
                        if (!request || request.userId != req.user._id || ![0, 1].includes(request.status))
                            return ChildRouter.responseError("Yêu cầu không hợp lệ", res);

                        await RequestBuyProductModel.MODEL.updateById(req.params.id, {status: 5});
                        await NotificationModel.MODEL.addNewNotification(request.userId, req.user._id, 'huỷ đơn hàng', 6, io, {
                            message: htmlspecialchars(message || ''),
                            typeService: 0,
                            requestId: request._id,
                            orderId: request.orderId,
                            storeId: request.shopId,
                        });
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/da-nhan-hang.html/:id': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let request = await RequestBuyProductModel.MODEL.getRequestById(req.params.id);
                        if (!request || request.userId != req.user._id || ![2].includes(request.type))
                            return ChildRouter.responseError("Yêu cầu không hợp lệ", res);

                        if (request.type != 2) {
                            return ChildRouter.responseError('Yêu cầu không hợp lệ', res);
                        }

                        await RequestBuyProductModel.MODEL.updateById(req.params.id, {type: 3});
                        NotificationModel.MODEL.addNewNotification(request.storeId, req.user._id, 'xác nhận đã nhận hàng', 8, io, {
                            typeService: 0,
                            requestId: request._id,
                            orderId: request.orderId,
                            storeId: request.shopId,
                        });

                        let settingAd = await SettingAdminModel.MODEL.getSettingAdmin();
                        let userStore = await UserModel.MODEL.getUsersById(request.storeId);

                        let totalMoney = 0;
                        request.products.forEach(item => {
                            totalMoney += Number(item.price) * Number(item.count)
                        });

                        if (request.couponId && request.couponId.trim() != '') {
                            let coupon = await CouponModel.MODEL.getDataById(request.couponId);
                            if (coupon) {
                                let valueDiscount = 0;
                                if (coupon.type == 1) {
                                    valueDiscount = totalMoney * Number(coupon.value) / 100;
                                } else {
                                    valueDiscount = (totalMoney >= Number(coupon.value)) ? Number(coupon.value) : totalMoney;
                                }
                                totalMoney = Number(totalMoney - valueDiscount).toFixed(2);
                            }
                        }

                        if (userStore.fee == 1) {
                            let fee = (totalMoney * Number(settingAd.percent)) / 100;
                            await UserModel.MODEL.updateUser(request.storeId, {$inc: {funds: -fee}});
                            await FundLogModel.MODEL.addFundLog({
                                userId: request.storeId,
                                storeId: request.shopId,
                                requestId: req.params.id,
                                fee: fee
                            })
                        }

                        if (request.payments == 3 && request.isPayOnline == 1) {
                            // Nếu đây là đơn hàng thanh toán online, tiến hành cộng tiền cho user
                            await UserModel.MODEL.updateUser(req.user._id, {$inc: {wallet: totalMoney}});

                            //Cộng quỹ online cho hệ thống
                            WalletOnlineModel.MODEL.updateWallet(
                                {$inc: {storeValue: Number(totalMoney)}}
                            );
                        }

                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/huy-dich-vu.html/:id/:type': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {id, type} = req.params;
                        let message = req.body.message;
                        const {reasonCancel, noteCancel} = req.body;

                        type = Number(type);
                        let data = null;
                        let parentModal = null;
                        switch (type) {
                            case 1: // phong kham
                                data = await BookExaminationModel.MODEL.getBookExaminationById(id);
                                parentModal = BookExaminationModel.MODEL;
                                break;
                            case 2: // khach san
                                data = await BookRoomModel.MODEL.getBookRoomById(id);
                                parentModal = BookRoomModel.MODEL;
                                break;
                            case 3: // spa
                                data = await BookSpaModel.MODEL.getBookSpaById(id);
                                parentModal = BookSpaModel.MODEL;
                                break;
                        }
                        if (!data || data.userId != req.user._id || ![0, 1].includes(data.status))
                            return ChildRouter.responseError("Yêu cầu không hợp lệ", res);

                        await parentModal.updateById(id, {status: 2, reasonCancel, noteCancel});
                        if (!data.storeManagerId) {
                            let service = await ServicesModel.MODEL.getServicesById(data.storeId);
                            if (service) data.storeManagerId = service.userId;
                        }
                        NotificationModel.MODEL.addNewNotification(data.storeManagerId, req.user._id, 'huỷ lịch hẹn', 7, io, {
                            message: htmlspecialchars(message || ''),
                            typeService: type,
                            bookId: data._id,
                            orderId: data.orderId,
                            storeId: data.shopId,
                        });
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/tai-ngan-hang-chu.html/:userId': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let banks = await AccountBankModel.MODEL.getBankByCondition({userId: req.params.userId});
                        return ChildRouter.responseSuccess("Thành công", res, {banks});
                    }],
                },
            },

            '/chi-tiet-cua-hang.html/:storeId': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let store = await ServicesModel.MODEL.getServicesById(req.params.storeId);
                        let products = await ProducModel.MODEL.getProductForStore(req.params.storeId);
                        return ChildRouter.responseSuccess("Thanh cong", res, {store, products})
                    }],
                },
            },

            '/change-status-schedule-done/:scheduleId.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'price', type: this.dataType.number, name: 'Chi phí dịch vụ'},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {index} = req.query;
                        let {price} = req.body;
                        index = Number(index);
                        if ([1, 2, 3].includes(index)) {
                            let booking;
                            if (index == 1) {
                                booking = await BookExaminationModel.MODEL.getBookExaminationById(req.params.scheduleId);
                                if (booking.status == 3) return ChildRouter.responseError('Dịch vụ đã được xác nhận hoàn thành', res);
                                if (booking.status != 1) return ChildRouter.responseError('Dịch vụ đã bị huỷ trước đó. Vui lòng kiểm tra lại trang thái dịch vụ', res);
                                if (booking.userId != req.user._id) {
                                    return ChildRouter.responseError('Vượt quyền truy cập', res);
                                }
                                await BookExaminationModel.MODEL.updateBookExamination(req.params.scheduleId, {
                                    status: 3,
                                    price
                                })
                            } else if (index == 2) {
                                booking = await BookRoomModel.MODEL.getBookRoomById(req.params.scheduleId);
                                if (booking.status == 3) return ChildRouter.responseError('Dịch vụ đã được xác nhận hoàn thành', res);
                                if (booking.status != 1) return ChildRouter.responseError('Dịch vụ đã bị huỷ trước đó. Vui lòng kiểm tra lại trang thái dịch vụ', res);
                                if (booking.userId != req.user._id) {
                                    return ChildRouter.responseError('Vượt quyền truy cập', res);
                                }
                                await BookRoomModel.MODEL.updateBookRoom(req.params.scheduleId, {status: 3, price})
                            } else if (index == 3) {
                                booking = await BookSpaModel.MODEL.getBookSpaById(req.params.scheduleId);
                                if (booking.status == 3) return ChildRouter.responseError('Dịch vụ đã được xác nhận hoàn thành', res);
                                if (booking.status != 1) return ChildRouter.responseError('Dịch vụ đã bị huỷ trước đó. Vui lòng kiểm tra lại trang thái dịch vụ', res);
                                if (booking.userId != req.user._id) {
                                    return ChildRouter.responseError('Vượt quyền truy cập', res);
                                }
                                await BookSpaModel.MODEL.updateBookSpa(req.params.scheduleId, {status: 3, price})
                            }
                            let manager = await UserModel.MODEL.getUsersById(booking.storeManagerId);
                            if (manager.fee == 1) {
                                let settingAd = await SettingAdminModel.MODEL.getSettingAdmin();
                                await UserModel.MODEL.updateUser(manager._id, {$inc: {funds: -Number(settingAd.fee)}});
                                await FundLogModel.MODEL.addFundLog({
                                    userId: booking.storeManagerId,
                                    storeId: booking.storeId,
                                    bookId: req.params.scheduleId,
                                    fee: settingAd.fee,
                                    typeService: index
                                })
                            }
                            return ChildRouter.responseSuccess('Thành công', res);
                        } else {
                            return ChildRouter.responseError('Thông tin không hợp lệ', res);
                        }
                    }],
                },
            },

            '/chi-tiet-lich-hen/:scheduleId.html': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {index} = req.query;
                        index = Number(index);
                        if ([1, 2, 3].includes(index)) {
                            let dataBook = null
                            if (index == 1) {
                                dataBook = await BookExaminationModel.MODEL.getBookExaminationById(req.params.scheduleId)
                            } else if (index == 2) {
                                dataBook = await BookRoomModel.MODEL.getBookRoomById(req.params.scheduleId)
                            } else if (index == 3) {
                                dataBook = await BookSpaModel.MODEL.getBookSpaById(req.params.scheduleId)
                            }
                            let service = await ServicesModel.MODEL.getServicesById(dataBook.storeId)
                            dataBook.storeName = service.name;
                            dataBook.storeAddress = service.address;
                            dataBook.type = index;
                            return ChildRouter.renderOrResponse(req, res, {dataBook});
                        } else {
                            return ChildRouter.redirect(res, '/store/quan-ly-lich-hen.html');
                        }
                    }],
                },
            },

            '/lich-su-tim-kiem.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {type} = req.query;
                        let timKiemGanDay = await TimKiemModal.MODEL.findSearchUser(req.user._id, type);
                        let moiNhat = [];
                        if (type == 'sp') {
                            moiNhat = await ProducModel.MODEL.getProductMoiNhat();
                        } else {
                            moiNhat = await ServicesModel.MODEL.getServicesByCondition({
                                type: type == 'ks' ? 2 : type == 'pk' ? 1 : 3,
                            }, {createAt: -1}, 30);
                        }

                        return ChildRouter.responseSuccess("thanh cong", res, {timKiemGanDay, moiNhat});
                    }],
                },
            },

            '/them-tim-kiem.html': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {searchId, type} = req.query;
                        await TimKiemModal.MODEL.addSearch(req.user._id, searchId, type);
                        return ChildRouter.responseSuccess("thanh cong", res);
                    }],
                },
            },

            '/get-user-setting.html': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let settings = await UserSettingModel.MODEL.getSetting(req.user._id);
                        return ChildRouter.responseSuccess("thanh cong", res, {settings});
                    }],
                },
            },

            '/update-user-setting.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let data = req.body;
                        await UserSettingModel.MODEL.updateSetting(req.user._id, data);
                        return ChildRouter.responseSuccess("thanh cong", res);
                    }],
                },
            },

            '/ho-tro.html': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let data = await HoTroModel.MODEL.getPosts();
                        return ChildRouter.responseSuccess("thanh cong", res, data);
                    }],
                },
            },

            '/calculate-order': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },
                methods: {
                    validate: [
                        {key: 'subTotal', type: this.dataType.string, name: 'subTotal', min: 5},
                        // {key: 'storeId', type: this.dataType.string, name: 'storeId', min: 5},
                    ],
                    post: [async function (req, res) {
                        const userIdReq = req.user._id;
                        let {code, subTotal, feeShip, storeId, bookingType} = req.body;
                        const rs = await BookingUtil.calculateCouponShop(userIdReq, code, subTotal, feeShip, storeId)
                        return ChildRouter.responseSuccess("Thành công", res, rs);
                    }],
                },
            },
            '/calculate-order-service': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },
                methods: {
                    validate: [
                        {key: 'subTotal', type: this.dataType.string, name: 'subTotal', min: 5},
                        // {key: 'storeId', type: this.dataType.string, name: 'storeId', min: 5},
                    ],
                    post: [async function (req, res) {
                        const userIdReq = req.user._id;
                        let {code, subTotal, feeShip, storeId, bookingType} = req.body;
                        const rs = await BookingUtil.calculateCouponService(userIdReq, code, subTotal, feeShip, storeId)
                        return ChildRouter.responseSuccess("Thành công", res, rs);
                    }],
                },
            },

            '/get-user-data.html': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        if (user) {
                            delete user.password;
                            return ChildRouter.responseSuccess("Thành công", res, {user});
                        }
                    }],
                },
            },

            '/xoa-order.html/:paymentId': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        if (req.params.paymentId && req.params.paymentId.trim().length > 0) {
                            console.log('paymentId-2', req.params.paymentId)
                            await RequestBuyProductModel.MODEL.removeDataWhere({paymentId: req.params.paymentId})
                            await VpnPayModel.MODEL.removeDataWhere({orderId: req.params.paymentId})
                        }
                        return ChildRouter.responseSuccess("Thành công", res);

                    }],
                },
            },

            '/xoa-order-of-spa.html/:orderId': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {type} = req.query; //?type=
                        let {orderId} = req.params;
                        if (orderId && type) {
                            console.log('req.params.orderId', orderId)
                            if (Number(type) == TypeServices.SPA)
                                await BookSpaModel.MODEL.removeDataWhere({orderId})
                            if (Number(type) == TypeServices.CLINIC)
                                await BookExaminationModel.MODEL.removeDataWhere({orderId})
                            if (Number(type) == TypeServices.HOTEL)
                                await BookRoomModel.MODEL.removeDataWhere({orderId})
                            if (Number(type) == TypeServices.SHOP)
                                await RequestBuyProductModel.MODEL.removeDataWhere({orderId})
                            // xoa ở bảng thanh toán
                            await VpnPayModel.MODEL.removeDataWhere({orderId})
                        } else {
                            return ChildRouter.responseError("Cần truyền đủ tham số type và orderId", res);
                        }
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },

            '/calculator-shipping': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'products', type: this.dataType.array, name: 'Sản phẩm', min: 1},
                                {key: 'storeId', type: this.dataType.string, name: 'Thương hiệu', min: 5},
                                {
                                    key: 'shippingService',
                                    type: this.dataType.string,
                                    name: 'Đơn vị vận chuyển',
                                    min: 1
                                },
                            ],
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let user = await UserModel.MODEL.getUsersById(req.user._id);
                        let {province, district, ward, street, location, shippingService, storeId, products} = req.body;
                        products = products || [];

                        if (!user || user.type != 2) {
                            return ChildRouter.responseError("Chỉ có người dùng MaxQ.vn mới được mua hàng trên ứng dụng", res);
                        }

                        switch (shippingService) {
                            case 'ghtk':

                                let data = await GHTK.tinhPhiVanChuyenThanhToan(storeId, products, {
                                    province,
                                    district,
                                    ward,
                                    street,
                                    location,
                                });
                                if (data) {
                                    if (data.error) {
                                        return ChildRouter.responseError(data.message, res, data);
                                    } else {
                                        return ChildRouter.responseSuccess("Thành công", res, data);
                                    }
                                } else {
                                    return ChildRouter.responseError("Có lỗi khi tính phí vận chuyển", res);
                                }

                                break;
                            default:
                                return ChildRouter.responseError("Đơn vị vận chuyển không hợp lệ", res);
                                break;
                        }
                    }],
                },
            },
            '/download-image/': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {path} = req.query;
                        if (path) {
                            try {
                                if (fs.existsSync('.' + path)) {
                                    //file exists
                                    return ChildRouter.responseSuccess('Thành công', res, {url: `${CfApp.domain}${path}`});
                                } else {
                                    let rs = await FileUtils.getDropboxImage(path);
                                    if (rs.err === false) {
                                        // + pathFile.dirname(path)
                                        const dir = '.' + pathFile.dirname(path)
                                        if (!fs.existsSync(dir)) {
                                            fs.mkdirSync(dir, {recursive: true});
                                        }
                                        const filename = pathFile.parse(path).name
                                        base64Img.img(rs.url, dir, filename, function (err, filepath) {
                                            return ChildRouter.responseSuccess('Thành công', res, {url: `${CfApp.domain}/${filepath}`});
                                        });
                                        // return ChildRouter.responseSuccess('Thành công',res,{url: rs.url});
                                    } else {
                                        return ChildRouter.responseError("Xảy ra lỗi.", res, {path});
                                    }
                                }
                            } catch (err) {
                                console.error(err)
                            }

                        } else {
                            return ChildRouter.responseError("Vui lòng nhập đường dẫn hợp lệ.", res);
                        }
                    }],
                },
            },
            '/check-coupon.html': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {
                        let {code} = req.body;
                        code = code.toLowerCase();
                        let coupon = await CouponModel.MODEL.getDataWhere({
                            code,
                            status: 1
                        }, CouponModel.MODEL.FIND_ONE(), {createAt: -1});
                        if (coupon) {
                            let currentTime = new Date().getTime();
                            if (Number(coupon.currentBooking) < Number(coupon.countBooking)
                                && currentTime >= coupon.startTime && currentTime <= coupon.endTime) {

                                // Kiểm tra coupon limit by day.
                                let userId = req.user._id;

                                let getCouponUser = await CouponUserModel.MODEL.getDataWhere({
                                    coupon: coupon.code,
                                    userId
                                }, CouponUserModel.MODEL.FIND_ONE(), {createAt: -1});

                                if (getCouponUser) {
                                    let createAt = Number(getCouponUser.createAt);
                                    let timeOneDay = 1 * 24 * 60 * 60 * 1000; // Cố định 1 day : tính theo Timestamp milliseconds
                                    if ((currentTime - createAt) < timeOneDay) {
                                        return ChildRouter.responseError("Bạn đã dùng mã này trong 24 giờ trước, vui lòng sử dụng sau.", res);
                                    }
                                }
                                return ChildRouter.responseSuccess("Thành công", res, {coupon});

                            } else {
                                return ChildRouter.responseError("Mã giảm giá không hợp lệ hoặc đã hết hạn", res);
                            }
                        } else {
                            return ChildRouter.responseError("Mã giảm giá không hợp lệ hoặc đã hết hạn", res);
                        }
                    }],
                },
            },
            // API FOR PROMOTION
            // For Home: Lấy 1 promotion mới nhất
            '/get-promotion-for-home': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let dataServices = null;
                        let data = await PromotionModel.MODEL.getOnePromotionByCondition({});
                        if (data && data.stores) {
                            let serviceIds = data.stores;
                            if (serviceIds) {
                                dataServices = await ServicesModel.MODEL.getListOfPromotion(serviceIds)
                            }
                        }
                        return ChildRouter.responseSuccess("Thành công", res, {...data, dataServices});
                    }],
                },
            },
            '/get-promotions': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let conditions = {
                            status: 1 // chỉ get status hiện
                        }
                        let data = await PromotionModel.MODEL.getPromotionByCondition(conditions);
                        return ChildRouter.responseSuccess("Thành công", res, {...data});
                    }],
                },
            },
            //get-promotion-by-id?id=xxx
            '/get-promotion-by-id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {id} = req.query;
                        let dataServices = null;
                        let data = await PromotionModel.MODEL.getPromotionById(id);
                        if (data) {
                            let serviceIds = data.stores ? data.stores : null;
                            if (serviceIds) {
                                dataServices = await ServicesModel.MODEL.getListOfPromotion(serviceIds)
                            }
                        }
                        return ChildRouter.responseSuccess("Thành công", res, {...data, dataServices});
                    }],
                },
            },
            // END API PROMOTION
            '/get-list-province': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let data = await ServicesModel.MODEL.getServicesWithProvince();
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-provinces': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let data = await ProvinceModel.MODEL.getByCondition(
                            {parentId: null}, {createAt: 1});
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-province-by-name': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let {name} = req.query;
                        name = StringUtils.removeUtf8(name)
                        let data = await ProvinceModel.MODEL.getOneByCondition({nameUTF: name});
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-district-by-province': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        let {province} = req.query;
                        let data = [];
                        province = province ? province : '';
                        if (!province && req.headers['province']) {
                            province = req.headers['province']
                        }
                        province = province.replace('hcm', 'thanh_pho_ho_chi_minh')
                        province = StringUtils.removeUtf8(province);
                        let condition = {level: 2}
                        if (province) {
                            condition.parentNameUTF = {$regex: province}
                            data = await ProvinceModel.MODEL.getByCondition(condition, {createAt: 1});
                        }

                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            // Pet API
            // userId: String,
            // name: String, // tên xe
            // carIdNo: String, // bsx
            // description: String,
            // photo: String,
            // frameNo: String, // số khung
            // engineNo: String, // số máy
            // carBrandId: String, // thương hiệu ID
            // categoryId: String, // dòng xe ID
            // carTypeId: String, // loại xe ID
            // color: String,
            // dob: Number, // năm sản xuất
            // datePickup: String,
            // gpsId: String,
            '/add-car': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên thú cưng', min: 3},
                                {key: 'photo', type: this.dataType.string, name: 'Ảnh xe'},
                                {key: 'carIdNo', type: this.dataType.string, name: 'Biển số xe'},
                                {key: 'carBrandId', type: this.dataType.string, name: 'Thương hiệu xe'},
                                {key: 'categoryId', type: this.dataType.string, name: 'Dòng xe'},
                                {key: 'carTypeId', type: this.dataType.string, name: 'Loại xe xe'},
                                {key: 'dob', type: this.dataType.string, name: 'Năm sản xuất'},
                                {key: 'frameNo', type: this.dataType.string, name: 'Số khung'},
                                {key: 'engineNo', type: this.dataType.string, name: 'Số máy'},
                                {key: 'gpsId', type: this.dataType.string, name: 'GPS ID'}
                            ]
                        },
                    },
                },
                methods: {
                    post: [async function (req, res) {
                        let obj = {
                            userId: req.user._id,
                            ...req.body
                        };
                        let rs = await CarModel.MODEL.add(obj)
                        return ChildRouter.responseSuccess('Thành công', res, rs);
                    }]
                },
            },
            '/edit-car': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'name', type: this.dataType.string, name: 'Tên thú cưng', min: 3},
                                {key: 'photo', type: this.dataType.string, name: 'Ảnh xe'},
                                {key: 'carIdNo', type: this.dataType.string, name: 'Biển số xe'},
                                {key: 'carBrandId', type: this.dataType.string, name: 'Thương hiệu xe'},
                                {key: 'categoryId', type: this.dataType.string, name: 'Dòng xe'},
                                {key: 'carTypeId', type: this.dataType.string, name: 'Loại xe xe'},
                                {key: 'dob', type: this.dataType.string, name: 'Năm sản xuất'},
                                {key: 'frameNo', type: this.dataType.string, name: 'Số khung'},
                                {key: 'engineNo', type: this.dataType.string, name: 'Số máy'},
                                {key: 'gpsId', type: this.dataType.string, name: 'GPS ID'}
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {id} = req.query;

                        let obj = {
                            ...req.body
                        };

                        for (let key in obj) {
                            if (!obj[key] && obj[key] != 0) {
                                delete obj[key]
                            }
                        }
                        let rs = await CarModel.MODEL.updateById(id, obj)
                        if (rs.ok) {
                            return ChildRouter.responseSuccess('Thành công', res, obj);
                        } else {
                            return ChildRouter.responseError("Đã xảy ra lỗi", res, rs)
                        }

                    }]
                },
            },
            '/delete-car': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {id} = req.query;
                        const userId = req.user._id;
                        let data = await CarModel.MODEL.getDataById(id);
                        if (data && data.userId == userId) {
                            let rs = await CarModel.MODEL.removeDataById(id);
                            return ChildRouter.responseSuccess("Thành công", res, data);
                        } else {
                            return ChildRouter.responseError('Đã xảy ra lỗi', res, data)
                        }

                    }],
                },
            },
            '/get-car': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {id} = req.query;
                        let data = await CarModel.MODEL.getPet(id);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-list-car': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const userId = req.user._id;
                        let data = await CarModel.MODEL.getListPet({userId});
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/list-categories-car': { // dòng xe
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {parentId, id} = req.query;
                        let condition = {
                            status: 1
                        }
                        if (parentId) {
                            condition.parentId = parentId
                        }
                        if (id) {
                            condition._id = ObjectId(id)
                        }
                        let data = await PetCategoryModel.MODEL.getByCondition(condition);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/list-type-cars': { // loại xe
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {parentId, id} = req.query;
                        let condition = {
                            status: 1
                        }
                        if (parentId) {
                            condition.parentId = parentId
                        }
                        if (id) {
                            condition._id = ObjectId(id)
                        }
                        let data = await PetCategoryModel.MODEL.getByCondition(condition);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/list-brand-cars': { // thương hiệu
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {parentId, id} = req.query;
                        let condition = {
                            status: 1
                        }
                        if (parentId) {
                            condition.parentId = parentId
                        }
                        if (id) {
                            condition._id = ObjectId(id)
                        }
                        let data = await PetCategoryModel.MODEL.getByCondition(condition);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-pet-categories-parent': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let condition = {
                            status: 1,
                            parentId: ''
                        }
                        let data = await PetCategoryModel.MODEL.getByCondition(condition);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-shipping-info/:orderId': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const orderId = req.params.orderId;
                        const result = await ShippingModel.MODEL.getByCondition({
                            partner_id: orderId
                        })
                        return ChildRouter.responseSuccess("Thành công", res, result);
                    }],
                },
            },
            '/categories-by-type': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let arr = req.query.cat // 0,1,2,3,4
                        arr = arr.split(',').map(Number)
                        let categories = await CategoryModel.MODEL.getCategoryByCondition({
                            status: 1,
                            type: {$in: arr}
                        });
                        return ChildRouter.responseSuccess("Thành công", res, {categories});
                    }],
                },
            },
            '/get-news': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let data = await NewsModel.MODEL.getNewsByCondition({
                            status: 0
                        }, 20);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-news-detail/:id': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let id = req.params.id
                        let data = await NewsModel.MODEL.getDataById(id);
                        
                        // Tăng lượt xem nếu tin tức tồn tại
                        if (data) {
                            // Đảm bảo tương thích ngược với các bản ghi chưa có trường views
                            const currentViews = data.views || 0;
                            // Cập nhật trực tiếp trong DB
                            await NewsModel.MODEL.updateNews(id, {
                                views: currentViews + 1
                            });
                            
                            // Cập nhật lại dữ liệu để trả về views mới nhất
                            data.views = currentViews + 1;
                        }
                        
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/get-user-viewed': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const userId = req.user._id;
                        let {field, page, limit} = req.query;
                        page = Number(page) || 1;
                        limit = Number(limit) || 10;
                        let fieldList = ['storeIds', 'spaIds', 'clinicIds', 'hotelIds', 'productIds']

                        if (!fieldList.includes(field)) {
                            return ChildRouter.responseError('Đã xảy ra lỗi', res);
                        }
                        let data = [];
                        let ids;
                        const condition = {userId}
                        const dataUserView = await UserViewedModel.MODEL.getViewByCondition(condition);

                        switch (field) {
                            case 'productIds':
                                ids = dataUserView && dataUserView.productIds ? dataUserView.productIds : [];
                                data = await ProductModel.MODEL.getProductPageByIds(ids, page, limit)
                                break;
                        }
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }]
                },
            },
            '/update-user-viewed': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'field', type: this.dataType.string, name: 'Field'},
                                {key: 'fieldId', type: this.dataType.string, name: 'Field ID'},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        const userId = req.user._id;
                        let {
                            field,
                            fieldId,
                        } = req.body;
                        let fieldList = ['storeIds', 'spaIds', 'clinicIds', 'hotelIds', 'productIds']

                        if (!fieldList.includes(field)) {
                            return ChildRouter.responseError('Đã xảy ra lỗi', res);
                        }
                        const rs = await UserViewedModel.MODEL.updateViewed(
                            field,
                            {userId},
                            fieldId
                        )
                        return ChildRouter.responseSuccess("Thành công", res, rs);
                    }]
                },
            },
            '/product-search': {
                config: {
                    auth: [this.roles.all],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {
                            search,
                            rate,
                            price,
                            shipping,
                            page,
                            limit,
                            order,
                            orderby,
                        } = req.query;
                        page = Number(page) || 1;
                        limit = Number(limit) || 10;
                        rate = Number(rate) || 0;
                        order = order || 'sales';
                        orderby = Number(orderby);

                        let keySort = '';

                        if (order) {
                            switch (order) {
                                case 'sales':
                                    keySort = 'revenue';
                                    break;
                                case 'price':
                                    keySort = 'price';
                                    break;
                                case 'newest':
                                    // keySort = 'createAt';
                                    keySort = 'code';
                                    orderby = -1;
                                    break;
                                default:
                                    keySort = order
                                    break;
                            }
                        }
                        let obSort = {
                            [keySort]: orderby
                        }

                        let condition = {
                            status: 1
                        };
                        let conditionOr = {};
                        if (search) {
                            conditionOr = ['nameUTF', 'descriptionUTF', 'addressUTF'].map(key => ({
                                [key]: {
                                    $regex: StringUtils.removeUtf8(search)
                                }
                            }));
                            condition['$or'] = conditionOr;
                        }
                        if (rate) {
                            condition.rate = {
                                $gte: rate
                            }
                        }
                        if (shipping) {
                            condition.transport = {
                                $regex: shipping,
                                $options: 'i'
                            };
                        }

                        if (price) {
                            price = price.split("-");
                            let minPrice = price[0] ? Number(price[0]) : 0;
                            let maxPrice = price[1] ? Number(price[1]) : 0;
                            condition.price = {}
                            if (minPrice) {
                                condition.price.$gte = minPrice;
                            }
                            if (maxPrice) {
                                condition.price.$lte = maxPrice;
                            }
                        }

                        const data = await ProducModel.MODEL.getProductByConditionWithPageLookup(condition, page, limit, {}, obSort);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            // Product Rating.
            '/product-rate': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'rate',
                                    type: this.dataType.number,
                                    name: 'Số sao',
                                    min: 0,
                                    max: 5
                                },
                                {
                                    key: 'content',
                                    type: this.dataType.string,
                                    name: 'Nội dung',
                                    min: 1,
                                    max: 255
                                },
                                {
                                    key: 'like',
                                    type: this.dataType.number,
                                    name: 'Số lượt thích!',
                                },
                                {
                                    key: 'productId',
                                    type: this.dataType.string,
                                    name: 'Sản phẩm',
                                    min: 1,
                                    max: 255
                                },
                            ]
                        }
                    }
                }, methods: {
                    post: [async function (req, res) {
                        let {storeId, content, like, rate, productId} = req.body;
                        const userId = req.user._id;
                        await ProductRateModel.MODEL.addComment({
                            userId,
                            storeId,
                            productId,
                            content,
                            like,
                            rate
                        })

                        ProductRateModel.MODEL.getCommentRateTotal({productId}).then(result => {
                            const totalRateValue = result.rateTotalValue
                            ProductModel.MODEL.updateById(productId, {rate: totalRateValue})
                        }).catch(err => {
                            console.log('Tong hop rate loi: ', err)
                        })
                        return ChildRouter.responseSuccess("Gửi đánh giá thành công", res);
                    }]
                },
            },
            '/product-rate/:id': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    get: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {
                                    key: 'content',
                                    type: this.dataType.string,
                                    name: 'Nội dung',
                                    min: 1,
                                    max: 255
                                },
                            ]
                        }
                    }
                }, methods: {
                    get: [async function (req, res) {
                        let productId = req.params.id;
                        let {page, limit} = req.query;

                        let userId = req.user._id;

                        page = Number(page) || 1;
                        limit = Number(limit) || 10;

                        let commentData = await ProductRateModel.MODEL.getCommentByConditionWithPage({
                            productId
                        }, page, {createAt: -1}, limit, 'userId');

                        let totalRate = await ProductRateModel.MODEL.getCommentRateTotal({
                            productId
                        })
                        let getRatingByUserId = await ProductRateModel.MODEL.getCommentByCondition({
                            productId,
                            userId
                        })
                        const totalPage = Math.ceil(totalRate.totalComment / limit);
                        return ChildRouter.renderOrResponse(req, res, {
                            commentData,
                            totalRate,
                            totalPage,
                            productId,
                            getRatingByUserId
                        });
                    }],
                    post: [async function (req, res) {
                        let {content, like, rate} = req.body;
                        const rateId = req.params.id;
                        const userId = req.user._id;
                        let data = await ProductRateModel.MODEL.getCommentById(rateId);
                        if (data && data.userId) {
                            if (data.userId != userId) {
                                return ChildRouter.responseError("Vượt quyền truy cập6", res);
                            }
                        }
                        await ProductRateModel.MODEL.updateComment(rateId, {
                            content,
                            like,
                            rate,
                        })
                        return ChildRouter.responseSuccess("Sửa thành công", res);
                    }]
                },
            },
            '/product-rate/delete/:id': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                }, methods: {
                    post: [async function (req, res) {
                        await ProductRateModel.MODEL.deleteComment(req.params.id);
                        return ChildRouter.responseSuccess("Xóa đánh giá thành công", res);
                    }]
                }
            },
            '/upload-image': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    upload: [{name: 'picture', maxCount: 1}],
                },

                methods: {
                    post: [async function (req, res) {
                        if (req.upload && req.upload.picture) {
                            return ChildRouter.responseSuccess("Thành công", res, {picture: req.upload.picture[0].path});
                        } else {
                            return ChildRouter.responseError("Ảnh không hợp lệ", res);
                        }
                    }]
                },
            },
            '/pet-logs': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'title', type: this.dataType.string, name: 'Tiêu đề', min: 3},
                                {key: 'petId', type: this.dataType.string, name: 'Thú cưng'},
                                {key: 'description', type: this.dataType.string, name: 'Nội dung'},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            petId,
                            title,
                            description,
                            photo,
                            dateTime,
                        } = req.body;

                        dateTime = dateTime ?? new Date().getTime();

                        let timeString = new Date(dateTime);
                        const dateTimeString = timeString.getMonth() + 1 + '/' + timeString.getFullYear();

                        let obj = {
                            userId: req.user._id,
                            petId,
                            title,
                            description,
                            photo,
                            dateTime,
                            dateTimeString
                        };
                        let rs = await PetLogModel.MODEL.add(obj)
                        return ChildRouter.responseSuccess('Thành công', res, rs);
                    }]
                },
            },
            '/pet-logs/:id': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {id} = req.params;
                        let data = await PetLogModel.MODEL.getLog(id);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                    post: [async function (req, res) {
                        let {
                            title,
                            description,
                            photo,
                            dateTime,
                        } = req.body;

                        dateTime = dateTime ?? new Date().getTime();

                        let timeString = new Date(dateTime);
                        const dateTimeString = timeString.getMonth() + 1 + '/' + timeString.getFullYear();


                        let {id} = req.params;

                        let obj = {
                            title,
                            description,
                            photo,
                            dateTime,
                            dateTimeString,
                        };
                        console.log(obj);

                        let rs = await PetLogModel.MODEL.updateById(id, obj)
                        if (rs.ok) {
                            return ChildRouter.responseSuccess('Thành công', res, obj);
                        } else {
                            return ChildRouter.responseError("Đã xảy ra lỗi", res, rs)
                        }

                    }]
                },
            },
            '/pet-logs/:id/delete': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        let {id} = req.params;
                        const userId = req.user._id;
                        let data = await PetLogModel.MODEL.getDataById(id);
                        if (data && data.userId == userId) {
                            let rs = await PetLogModel.MODEL.removeDataById(id);
                            return ChildRouter.responseSuccess("Thành công", res, data);
                        } else {
                            return ChildRouter.responseError('Đã xảy ra lỗi', res, data)
                        }

                    }],
                },
            },
            '/pet-logs-by-pet/:id': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },

                methods: {
                    get: [async function (req, res) {
                        const userId = req.user._id;
                        const petId = req.params.id;
                        let data = await PetLogModel.MODEL.getLogsGroupByDateYear({petId, userId});
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            // Pet gallery
            '/pet-gallery/update-photo': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'picture', type: this.dataType.string, name: 'Hình Ảnh', min: 3},
                                {key: 'petId', type: this.dataType.string, name: 'Thú cưng'},
                            ]
                        },
                    },
                },
                methods: {
                    post: [async function (req, res) {
                        const userId = req.user._id;
                        let {
                            picture,
                            petId,
                            petName
                        } = req.body;

                        let galleryId = null;
                        let data = null;
                        let getGallery = await PetGalleryModel.MODEL.getOneByCondition({
                            userId,
                            petId
                        })

                        // create gallery if not exist
                        if (!getGallery) {
                            let objNew = {
                                title: petName,
                                userId,
                                petId,
                                photos: []
                            }
                            getGallery = await PetGalleryModel.MODEL.add(objNew);
                            galleryId = getGallery && getGallery._id ? getGallery._id : null;
                        } else {
                            galleryId = getGallery._id;
                        }
                        if (galleryId) {
                            let photos = getGallery.photos;
                            photos.push(picture);
                            await PetGalleryModel.MODEL.updateById(galleryId, {photos})
                            data = await PetGalleryModel.MODEL.getOne(galleryId)
                        } else {
                            return ChildRouter.responseError("Xảy ra lỗi", res);
                        }
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/pet-gallery/delete-photo': {
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'picture', type: this.dataType.string, name: 'Hình Ảnh', min: 3},
                                {key: 'petId', type: this.dataType.string, name: 'Thú cưng'},
                            ]
                        },
                    },
                },
                methods: {
                    post: [async function (req, res) {
                        const userId = req.user._id;
                        let {
                            picture,
                            petId,
                        } = req.body;
                        let data = null;

                        let getGallery = await PetGalleryModel.MODEL.getOneByCondition({
                            userId,
                            petId
                        })
                        if (getGallery) {
                            const galleryId = getGallery._id;
                            let photos = getGallery.photos ? getGallery.photos : [];
                            const indexPhoto = photos.indexOf(picture);
                            if (indexPhoto > -1) {
                                photos.splice(indexPhoto, 1);
                            }
                            FileUtils.deleteFileDropBox(picture)
                            await PetGalleryModel.MODEL.updateById(galleryId, {photos})
                            data = await PetGalleryModel.MODEL.getOne(galleryId)
                        } else {
                            return ChildRouter.responseError("Xảy ra lỗi", res);
                        }
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/pet-gallery-by-pet-id/:petId': { // TODO: đang ko sử dụng vì dùng api v2
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        const userId = req.user._id;
                        const petId = req.params.petId;
                        let data = await PetGalleryModel.MODEL.getOneByCondition({petId, userId});
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/add-brand-favorite/:id': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {

                        let serviceId = req.params.id
                        let userId = req.user._id

                        const dataSearch = await DataSearchModel.MODEL.getOneByCondition({serviceId});
                        if (!dataSearch) {
                            return ChildRouter.responseError("Có lỗi xảy ra", res);
                        }
                        let usersFavorites = !dataSearch.usersFavorites ? [] : dataSearch.usersFavorites; // Check exist tags
                        usersFavorites.push(userId);
                        usersFavorites = _.uniq(usersFavorites); // Delete duplicate

                        // Update
                        const rs = await DataSearchModel.MODEL.updateWhereClause({
                            serviceId: serviceId
                        }, {usersFavorites: usersFavorites});

                        return ChildRouter.responseSuccess("Thành công", res);

                    }],
                },
            },
            '/remove-brand-favorite/:id': {
                config: {
                    auth: [this.roles.user],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {

                        let serviceId = req.params.id
                        let userId = req.user._id

                        const dataSearch = await DataSearchModel.MODEL.getOneByCondition({serviceId});
                        if (!dataSearch) {
                            return ChildRouter.responseError("Có lỗi xảy ra", res);
                        }
                        let usersFavorites = !dataSearch.usersFavorites ? [] : dataSearch.usersFavorites; // Check exist tags
                        usersFavorites = usersFavorites.filter(x => x !== userId)

                        // Update
                        const rs = await DataSearchModel.MODEL.updateWhereClause({
                            serviceId: serviceId
                        }, {usersFavorites: usersFavorites});

                        return ChildRouter.responseSuccess("Thành công", res);

                    }],
                },
            },
            '/join-promotion/:id': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {

                        let promotionId = req.params.id
                        let userId = req.user._id
                        let {branchId} = req.query

                        const promotion = await PromotionModel.MODEL.getPromotionById(promotionId);
                        if (!promotion) {
                            return ChildRouter.responseError("Có lỗi xảy ra", res);
                        }
                        let stores = !promotion.stores ? [] : promotion.stores; // Check exist tags
                        stores.push(branchId);
                        stores = _.uniq(stores); // Delete duplicate

                        // Update
                        const rs = await PromotionModel.MODEL.updateWhereClause({
                            _id: promotionId
                        }, {stores: stores});

                        return ChildRouter.responseSuccess("Thành công", res);

                    }],
                },
            },
            '/exit-promotion/:id': {
                config: {
                    auth: [this.roles.store],
                    post: 'json',
                },

                methods: {
                    post: [async function (req, res) {

                        let promotionId = req.params.id
                        let userId = req.user._id
                        let {branchId} = req.query

                        const promotion = await PromotionModel.MODEL.getPromotionById(promotionId);
                        if (!promotion) {
                            return ChildRouter.responseError("Có lỗi xảy ra", res);
                        }
                        let stores = !promotion.stores ? [] : promotion.stores; // Check exist tags
                        stores = stores.filter(x => x !== branchId)

                        // Update
                        const rs = await PromotionModel.MODEL.updateWhereClause({
                            _id: promotionId
                        }, {stores: stores});

                        return ChildRouter.responseSuccess("Thành công", res);

                    }],
                },
            },
            '/push-fcm-notification': {
                config: {
                    auth: [this.roles.user, this.roles.store],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'toUserId', type: this.dataType.string, name: 'ID người nhận', min: 24},
                                {key: 'fromUserId', type: this.dataType.string, name: 'ID người gửi', min: 24},
                                {key: 'message', type: this.dataType.string, name: 'Nội dung', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        const {toUserId, fromUserId, message} = req.body
                        await Notification.sendHasMessageNotification(toUserId, fromUserId, message); // push firebase
                        return ChildRouter.responseSuccess("Thành công", res);
                    }],
                },
            },
            '/logout': { // TODO: đang ko sử dụng vì dùng api v2
                config: {
                    auth: [this.roles.user],
                    get: 'json',
                },
                methods: {
                    get: [async function (req, res) {
                        const userId = req.user._id;
                        let data = await FcmTokensModel.MODEL.removeAllTokenForUser(userId);
                        return ChildRouter.responseSuccess("Thành công", res, data);
                    }],
                },
            },
            '/tinh-phi-tnds-oto': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            data,
                            env
                        } = req.body;
                        const config = await getRemoteConfig(env)

                        if (hangBaoHiem === 'PVI') {
                            // "Status": "00",
                            // "Message": "Thanh cong",
                            // "TotalFee": "576840",
                            // "phi_tndsbb": "576840",
                            // "phi_tnds": null,
                            // "phi_hang": null,
                            // "phi_lpx": "0",
                            // "phi_vcx": null,
                            // "ma_loaixe": "3007",
                            // "tylephi_chuanvcx": null,
                            // "tylephi_tinhvcx": null,
                            // "phi_pin": null,
                            // "tylephi_pin": null
                            const result = await axios.post(config['API_URL_BH_PVI'] + 'ManagerApplication/Get_TongPhi_Auto_TNDS', data)
                            const response = {...result?.data, TotalFee: result?.data?.TotalFee}
                            return ChildRouter.responseSuccess("Ok", res, response);
                        }
                        if (hangBaoHiem === 'BIC') {
                            const login = await bicLogin(config)
                            if (login) {
                                const fromDate = moment(data?.NgayDau, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const toDate = moment(data?.NgayCuoi, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const body = {
                                    "type": "mvl",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mvl'),
                                    "from_date": fromDate,
                                    "to_date": toDate,
                                    "vehicle_type": data?.ma_loaixe || data?.LoaiXe,
                                    "sit": data?.so_cho || 0,
                                    "tonnage": data?.ma_trongtai && data.ma_trongtai != '99999' ? data.ma_trongtai : 0,
                                    "purpose_of_use": data?.ma_mdsd,
                                    "sum_insured": data?.thamgia_laiphu ? parseInt(data?.mtn_laiphu) : 0,
                                }
                                const result = await axios.post(config['API_URL_BH_BIC'] + 'vehiclefee', body, {headers: buildHeaderBic(config, login?.data?.token)})

                                if (result?.data?.result_code == '000') {
                                    const totalFee = (result?.data?.data?.liability_insurance?.Premium || 0) + (result?.data?.data?.accident_insurance.fee || 0)
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: "00",
                                        Message: result?.data?.result_desc,
                                        ...result?.data,
                                        TotalFee: totalFee,
                                        TotalFeeNoVAT: (result?.data?.data?.liability_insurance?.NetPremium || 0) + (result?.data?.data?.accident_insurance.fee || 0)
                                    });
                                } else {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: result?.data?.result_code,
                                        Message: result?.data?.result_desc
                                    });
                                }
                            }
                        }

                        return ChildRouter.responseSuccess("Ok", res, {});
                    }],
                },
            },
            '/tinh-phi-vcx-oto': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1},
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            env,
                            data
                        } = req.body;
                        const config = await getRemoteConfig(env)

                        if (hangBaoHiem === 'PVI') {
                            if (data?.data_gddk) {
                                data['data_gddk'] = JSON.parse(data?.data_gddk || '[]');
                            }
                            const urlService = config['API_URL_BH_PVI'] + 'ManagerApplication/Get_TongPhi_Auto_VCX'
                            const result = await axios.post(urlService, data, {
                                headers: {
                                    'Content-Type': 'application/json'
                                }
                            })
                            const response = {...result?.data, TotalFee: result?.data?.TotalFee}
                            return ChildRouter.responseSuccess("Ok", res, response);
                        }

                        if (hangBaoHiem === 'BIC') {
                            const login = await bicLogin(config)
                            if (login) {
                                const fromDate = moment(data?.NgayDau, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const toDate = moment(data?.NgayCuoi, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const namSx = moment('01/' + data?.NamSX, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const body = {
                                    "type": "mv",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mv'),
                                    "from_date": fromDate,
                                    "to_date": toDate,
                                    "sum_insured": data?.GiaTriBH,
                                    "vehicle_type": data?.ma_loaixe || data?.LoaiXe,
                                    "customer_type_id": data?.MaSoThue ? 2 : 1,
                                    "purpose_of_use": data?.ma_mdsd,
                                    "date_of_first_registration": namSx,
                                    "deductible_id": 500000,
                                    "sit": data?.so_cho + '' || '0',
                                    "insurance_endor_list": data?.strdkbs,
                                    "accident_sum_insured": data?.ThamGiaLaiPhu ? parseInt(data?.MTNLaiPhu) : 0,
                                }
                                const urlService = config['API_URL_BH_BIC'] + 'vehiclefee'
                                const result = await axios.post(urlService, body, {headers: buildHeaderBic(config, login?.data?.token)})
                                if (result?.data?.result_code == '000') {
                                    const totalFee = (result?.data?.data?.liability_insurance?.phi_toithieu || 0) + (result?.data?.data?.accident_insurance.fee || 0)
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: "00",
                                        Message: result?.data?.result_desc,
                                        ...result?.data,
                                        TotalFee: totalFee,
                                        TotalFeeNoVAT: (result?.data?.data?.liability_insurance?.phi_toithieu || 0) + (result?.data?.data?.accident_insurance.fee || 0)
                                    });
                                } else {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: result?.data?.result_code,
                                        Message: result?.data?.result_desc
                                    });
                                }
                            }
                        }

                        return ChildRouter.responseSuccess("Ok", res, {});
                    }],
                },
            },
            '/tinh-phi-tnds-xm': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1},

                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            data,
                            env
                        } = req.body;

                        const config = await getRemoteConfig(env)
                        if (hangBaoHiem === 'PVI') {
                            const urlService = config['API_URL_BH_PVI'] + 'ManagerApplication/Get_Phi_XeMay'
                            const result = await axios.post(urlService, data)
                            const response = {...result?.data, phi_moto: result?.data?.TotalFee}
                            return ChildRouter.responseSuccess("Ok", res, response);
                        }

                        if (hangBaoHiem === 'BIC') {
                            const login = await bicLogin(config)
                            if (login) {
                                const urlService = config['API_URL_BH_BIC'] + 'mc'
                                const result = await axios.post(urlService, {
                                    "type": "mc",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mc')

                                }, {headers: buildHeaderBic(config, login?.data?.token)})
                                if (result?.data?.result_code == '000') {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: "00",
                                        Message: result?.data?.result_desc,
                                        ...result?.data,
                                        phi_moto: result?.data?.data?.phibaohiem[data?.loai_xe][data?.han_muc][parseInt(data?.ThoiHan + '')].phi,
                                        ...result?.data?.data?.phibaohiem[data?.loai_xe][data?.han_muc][parseInt(data?.ThoiHan + '')]
                                    });
                                } else {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: result?.data?.result_code,
                                        Message: result?.data?.result_desc
                                    });
                                }
                            }
                        }
                        return ChildRouter.responseSuccess("Ok", res, {});
                    }],
                },
            },
            '/tao-don-oto': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1},
                                {key: 'env', type: this.dataType.string, name: 'env', min: 1},

                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            data,
                            env,
                        } = req.body;

                        const config = await getRemoteConfig(env)
                        if (hangBaoHiem === 'PVI') {
                            if (data?.data_gddk) {
                                data['data_gddk'] = JSON.parse(data?.data_gddk || '[]');
                            }

                            //fix bug parse
                            if (data?.data_gddk && data['data_gddk'] == '[]')
                            {
                                data['data_gddk'] = []
                            }
                            // tự add sign & check sum nếu client ko truyền lên
                            if (!data?.CpId || !data?.Sign) {
                                data['CpId'] = config['PVI_CPID'];
                                data['Sign'] = md5(config['PVI_KEY'] + data?.ma_giaodich);
                            }
                            const urlService = config['API_URL_BH_PVI'] + 'ManagerApplication/TaoDon_Auto'
                            const result = await axios.post(urlService, data)
                            return ChildRouter.responseSuccess("Ok", res, {
                                ...result?.data,
                                Pr_key: `${result?.data?.Pr_key || ''}`
                            });
                        }

                        if (hangBaoHiem === 'BIC' && data?.type == 'tnds') {
                            const login = await bicLogin(config)
                            if (login) {
                                const fromDate = moment(data?.NgayDau, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const toDate = moment(data?.NgayCuoi, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const body = {
                                    "type": "mvl",
                                    "xe_nguoimua": data?.TenCongTy ? "2" : "1", // Kiểu khách hàng: 1 là cá nhân 2 là Doanh nghiệp, Tổ chức
                                    "xe_nguoimua_cmnd": "",
                                    "xe_nguoimua_masothue": data?.MaSoThue || "",
                                    "xe_mucdichsudung": data?.ma_mdsd,
                                    "xe_mucdichsudung_txt": data?.MucDichSuDung,
                                    "xe_loaixe": data?.ma_loaixe || data?.LoaiXe,
                                    "xe_loaixe_txt": data?.ma_loaixe_txt,
                                    "xe_chongoi": data?.so_cho,
                                    "xe_taitrong": data?.ma_trongtai && data.ma_trongtai != '99999' ? data.ma_trongtai : 0,
                                    "xe_tenchuxe": data?.TenChuXe,
                                    "xe_diachidangky": data?.DiaChiChuXe || "",
                                    "xe_tinhtrangbienso": "1",
                                    "xe_bienkiemsoat": data?.BienKiemSoat || "",
                                    "xe_sokhung": data?.SoKhung || "",
                                    "xe_somay": data?.SoMay || "",
                                    "xe_thoihantu": fromDate + " " + data?.GioDau || "00:00",
                                    "phi_tnds_truoc_thue": parseInt(data?.TotalFeeNoVAT || "0"),
                                    "phi_tnds_vat": parseInt(data?.PhiBHTNDSBB || "0") - parseInt(data?.TotalFeeNoVAT || "0"),
                                    "phi_tnds": data?.PhiBHTNDSBB,
                                    "xe_sotienthamgia": data?.mtn_laiphu && data?.mtn_laiphu != "0" ? parseInt(data?.mtn_laiphu) : 0,
                                    "xe_songuoithamgia": data?.so_nguoi && data?.so_nguoi != "0" ? parseInt(data?.so_nguoi) : 0,
                                    "tylephi_baohiemtainan": 0.3,
                                    "phi_baohiemtainan": 300000,
                                    "nguoinhan_gcn": data?.TenKH || "",
                                    "diachinhan_gcn": data?.DiaChiChuXe || "",
                                    "emailnhan_gcn": data?.EmailKH || "",
                                    "dienthoainhan_gcn": data?.DienThoai || "",
                                    "xuathoadon": data?.MaSoThue ? 1 : 0,
                                    "tendoanhnghiep": data?.TenCongTy || "",
                                    "diachidoanhnghiep": data?.DiaChiCongTy || "",
                                    "masothue_doanhnghiep": data?.MaSoThue || "",
                                    "tong_phi": data?.TongPhi,
                                    "chiet_khau": 0,
                                    "customer_hotendem": data?.TenChuXe,
                                    "customer_ten": data?.TenChuXe,
                                    "customer_dienthoai": data?.DienThoai || "",
                                    "customer_email": data?.EmailKH || "",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mvl'),
                                }
                                const urlService = config['API_URL_BH_BIC'] + 'createbill'
                                const result = await axios.post(urlService, body, {headers: buildHeaderBic(config, login?.data?.token)})
                                if (result?.data?.result_code == '000') {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: "00",
                                        Message: result?.data?.result_desc,
                                        ...result?.data,
                                        Pr_key: `${result?.data?.bill_id}`
                                    });
                                } else {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: result?.data?.result_code,
                                        Message: result?.data?.result_desc
                                    });
                                }
                            }
                        }

                        //TODO: test
                        if (hangBaoHiem === 'BIC' && data?.type == 'vcx') {
                            const login = await bicLogin(config)
                            if (login) {
                                const fromDate = moment(data?.NgayDau, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const toDate = moment(data?.NgayCuoi, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const namSX = moment('01/' + data?.NamSX, 'DD/MM/YYYY')
                                const phi_baohiemvatchat = parseFloat(data?.phi_baohiemvatchat);
                                var formData = new FormData();

                                JSON.parse(data?.data_gddk).forEach((e, index) => {
                                    const buff = Buffer.from(e.file_size, 'base64');
                                    if (index === 0) {
                                        formData.append('anhphuongtien[anh_phia_truoc_1]', buff, 'anh_phia_truoc_1.jpg');
                                    }
                                    if (index === 1) {
                                        formData.append('anhphuongtien[anh_phia_truoc_2]', buff, 'anh_phia_truoc_2.jpg');
                                    }
                                    if (index === 2) {
                                        formData.append('anhphuongtien[anh_phia_sau]', buff, 'anh_phia_sau.jpg');
                                    }
                                    if (index === 3) {
                                        formData.append('anhphuongtien[anh_suon_xe_1]', buff, 'anh_suon_xe_1.jpg');
                                    }
                                    if (index === 4) {
                                        formData.append('anhphuongtien[anh_suon_xe_2]', buff, 'anh_suon_xe_2.jpg');
                                    }
                                    if (index === 5) {
                                        formData.append('anhphuongtien[anh_dang_ky_xe]', buff, 'anh_dang_ky_xe.jpg');
                                    }
                                    if (index === 6) {
                                        formData.append('anhphuongtien[anh_cua_so_troi]', buff, 'anh_cua_so_troi.jpg');
                                    }
                                })

                                const md5Check = md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mv')
                                const values = {
                                    'type': 'mv',
                                    'xe_nguoimua': data?.TenCongTy ? "2" : "1",
                                    'xe_nguoimua_cmnd': '',
                                    'xe_nguoimua_masothue': '',
                                    'xe_doituong': data?.CheckXeMoiHoacTaiTuc ? "2" : "1",
                                    'xe_sohopdong': '',
                                    'xe_mucdichsudung': data?.MaMucDichSD + '',
                                    'xe_mucdichsudung_txt': data?.MucDichSuDung + '',
                                    'xe_loaixe': data?.LoaiXe + '',
                                    'xe_loaixe_txt': data?.ma_loaixe_txt,
                                    'xe_loaixe_code': data?.xe_loaixe_code || '',
                                    'xe_chongoi': data?.ChoNgoi + '',
                                    'xe_taitrong': data?.TrongTai && data.TrongTai != '99999' ? data.TrongTai + '' : '0',
                                    'xe_tenchuxe': data?.TenChuXe || '',
                                    'xe_diachidangky': data?.DiaChiChuXe || '',
                                    'xe_ngaydangkydau': namSX.format('DD-MM-YYYY'),
                                    'xe_namsanxuat': namSX.year(),
                                    'xe_tinhtrangbienso': '1',
                                    'xe_bienkiemsoat': data?.BienKiemSoat || '',
                                    'xe_sokhung': data?.SoKhung || '',
                                    'xe_somay': data?.SoMay || '',
                                    'xe_hangxe': data?.HangSanXuat || '',
                                    'xe_dongxe': data?.DongXeText || '',
                                    'xe_hangxe_txt': data?.HangSanXuatText || '',
                                    'xe_thoihantu': `${fromDate} ${data?.GioDau}`,
                                    'xe_thoihanden': `${toDate} ${data?.GioDau}`,
                                    'xe_giaxetheokhaibao': data?.GiaTriXe + '',
                                    'xe_sotienbaohiem': data?.GiaTriXe + '',
                                    'xe_khautru': '500000',
                                    'tylephi_text': 'phi_toithieu',
                                    'tylephi': data?.tylephi + '',
                                    'phi_baohiemvatchat': phi_baohiemvatchat,
                                    'phi_baohiemvatchat_truoc_thue': phi_baohiemvatchat,
                                    'phi_baohiemvatchat_vat': (phi_baohiemvatchat * 10) / 100,
                                    'xe_sotienthamgia': '0',
                                    'xe_songuoithamgia': data?.ChoNgoi + '',
                                    'tylephi_baohiemtainan': data?.tylephi_baohiemtainan || 0.1,
                                    'phi_baohiemtainan': '0',
                                    'nguoinhan_gcn': data?.TenChuXe || '',
                                    'diachinhan_gcn': data?.DiaChiChuXe || '',
                                    'emailnhan_gcn': data?.EmailKH || '',
                                    'dienthoainhan_gcn': data?.DienThoai || '',
                                    'xuathoadon': data?.MaSoThue ? '1' : '0',
                                    'tendoanhnghiep': data?.TenCongTy || '',
                                    'diachidoanhnghiep': data?.DiaChiCongTy || '',
                                    'masothue_doanhnghiep': data?.MaSoThue || '',
                                    'tong_phi': parseFloat(data?.TotalFee || data?.TongPhi || 0),
                                    'chiet_khau': '0',
                                    'customer_hotendem': data?.TenChuXe || '',
                                    'customer_ten': data?.TenChuXe || '',
                                    'customer_dienthoai': data?.DienThoai || '',
                                    'customer_email': data?.EmailKH || '',
                                    'checksum': md5Check,
                                }

                                Object.keys(values).forEach(key => {
                                    console.log(`${key}:${values[key]}`)
                                    formData.append(key, values[key])
                                })

                                if (data?.strMaDKBS) {
                                    data?.strMaDKBS.split(',').forEach(e => {
                                        formData.append('xe_dieukhoanbosung[]', e);
                                    })
                                }

                                try {

                                    //TODO: kiểu json ok, kiểu multiform data đang bị chết
                                    const urlService = config['API_URL_BH_BIC'] + 'createbill'
                                    const result = await axios.post(urlService, formData, {headers: {...buildHeaderBic(config, login?.data?.token, formData.getHeaders()['content-type']), 'Content-Length': formData.getLengthSync()}})
                                    if (result?.data?.result_code == '000') {
                                        return ChildRouter.responseSuccess("Ok", res, {
                                            Status: "00",
                                            Message: result?.data?.result_desc,
                                            ...result?.data,
                                            Pr_key: `${result?.data?.bill_id}`
                                        });
                                    } else {
                                        return ChildRouter.responseSuccess("Ok", res, {
                                            Status: result?.data?.result_code,
                                            Message: result?.data?.result_desc
                                        });
                                    }
                                } catch (e) {
                                    console.log(e)
                                    return ChildRouter.responseError("Error", res, {
                                        Status: 0,
                                        Message: e.toString()
                                    })
                                }
                            }
                        }

                        return ChildRouter.responseSuccess("Ok", res, {});
                    }],
                },
            },
            '/tao-don-tnds-xm': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1},

                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            data,
                            env
                        } = req.body;
                        const config = await getRemoteConfig(env)

                        if (hangBaoHiem === 'PVI') {
                            if (data?.ma_giaodich === 'CANCEL')
                            {
                                return ChildRouter.responseError('Bạn cần tự sinh mã giao dịch dạng số. Mã giao dịch ko thể là CANCEL')
                            } else if (!data?.ma_giaodich)
                            {
                                return ChildRouter.responseError('Bạn cần tự sinh mã giao dịch dạng số.')
                            }
                            const body = {
                                ma_giaodich: data?.ma_giaodich,
                                ten_nguoimua_bh: data?.ten_nguoimua_bh,
                                diachi_nguoimua_bh: data?.diachi_nguoimua_bh,
                                ngay_dau: data?.ngay_dau, // dd/MM/yyyy HH:mm
                                ngay_cuoi: data?.ngay_cuoi, // dd/MM/yyyyyy HH:mm
                                bien_kiemsoat: data?.bien_kiemsoat, // Biển kiểm soat
                                so_may: data?.so_may, // số máy nếu có
                                so_khung: data?.so_khung, // số khung nếu có
                                loai_xe: data?.loai_xe, // Lấy từ danh mục PVI cung cấp
                                nhan_hieu: data?.nhan_hieu, // Lấy từ danh mục PVI cung cấp
                                nam_sanxuat: data?.nam_sanxuat, //
                                ten_chuxe: data?.ten_chuxe, //
                                email: data?.email,
                                so_dienthoai: data?.so_dienthoai, //
                                dia_chi: data?.dia_chi,
                                thamgia_laiphu: data?.thamgia_laiphu || false,
                                muc_trachnhiem_laiphu: data?.muc_trachnhiem_laiphu || 0,
                                so_nguoi_tgia_laiphu: data?.so_nguoi_tgia_laiphu || 0,
                                an_bien_ks: data?.an_bien_ks || false,
                                CpId: '',
                                Sign: ''
                            }
                            // tự add sign & check sum nếu client ko truyền lên
                            if (!data?.CpId || !data?.Sign) {
                                body['CpId'] = config['PVI_CPID'];
                                body['Sign'] = md5(config['PVI_KEY'] + body.bien_kiemsoat + body.email + body.so_dienthoai + body.nhan_hieu + body.loai_xe + body.nam_sanxuat)
                            }
                            const result = await axios.post(config['API_URL_BH_PVI'] + 'ManagerApplication/TaoDon_XeMay', body)
                            return ChildRouter.responseSuccess("Ok", res, {...result?.data});
                        }
                        if (hangBaoHiem === 'BIC') {
                            const login = await bicLogin(config)
                            if (login) {
                                const fromDate = moment(data?.ngay_dau, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const toDate = moment(data?.ngay_cuoi, 'DD/MM/YYYY').format('DD-MM-YYYY')
                                const body = {
                                    "type": "mc",
                                    "xe_nguoimua": data?.TenCongTy ? "2" : "1", // Kiểu khách hàng: 1 là cá nhân 2 là Doanh nghiệp, Tổ chức
                                    "xe_nguoimua_cmnd": "",
                                    "xe_nguoimua_masothue": data?.MaSoThue || "",
                                    "xe_tenchuxe": data?.TenKH,
                                    "xe_diachidangky": data?.DiaChiKH || "",
                                    "xe_tinhtrangbienso": parseInt(data?.ThoiHan),
                                    "xe_bienkiemsoat": data?.bien_kiemsoat || "",
                                    "xe_sokhung": data?.so_khung || "",
                                    "xe_somay": data?.so_may || "",
                                    "xe_thoihantu": fromDate,
                                    // "phi_tnds_truoc_thue": parseInt(data?.TotalFeeNoVAT || "0"),
                                    // "phi_tnds_vat": parseInt(data?.PhiBHTNDSBB || "0") - parseInt(data?.TotalFeeNoVAT || "0"),
                                    // "phi_tnds": data?.PhiBHTNDSBB,
                                    // "xe_sotienthamgia": data?.mtn_laiphu && data?.mtn_laiphu != "0" ? parseInt(data?.mtn_laiphu) : 0,
                                    // "xe_songuoithamgia": data?.so_nguoi && data?.so_nguoi != "0" ? parseInt(data?.so_nguoi) : 0,
                                    // "tylephi_baohiemtainan":0.3,
                                    // "phi_baohiemtainan":300000,
                                    "nguoinhan_gcn": data?.TenKH || "",
                                    "diachinhan_gcn": data?.DiaChiKH || "",
                                    "emailnhan_gcn": data?.email || "",
                                    "dienthoainhan_gcn": data?.so_dienthoai || "",
                                    "xuathoadon": data?.MaSoThue ? 1 : 0,
                                    "tendoanhnghiep": data?.TenCongTy || "",
                                    "diachidoanhnghiep": data?.DiaChiCongTy || "",
                                    "masothue_doanhnghiep": data?.MaSoThue || "",
                                    "tong_phi": data?.phi + (data?.phi_baohiem_nguoi || 0),
                                    "chiet_khau": 0,
                                    "customer_hotendem": data?.TenKH,
                                    "customer_ten": data?.TenKH,
                                    "customer_dienthoai": data?.so_dienthoai || "",
                                    "customer_email": data?.email || "",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mc'),
                                    "xe_dungtichxilanh": 1,
                                    "xe_hanmucbaohiem": 0,
                                    "xe_thoibanbaohiem": 1,
                                    "phi_baohiem": data?.phi,
                                    "vat": data?.vat || 0,
                                    "phi_goc": data?.phi,
                                    "phi_baohiem_nguoi": data?.phi_baohiem_nguoi || 0,
                                    "nguoimua": 1,
                                    "nguoimua_cmnd": "123456789",
                                    "nguoimua_masothue": "",
                                }
                                const urlService = config['API_URL_BH_BIC'] + 'createbill'
                                const result = await axios.post(urlService, body, {headers: buildHeaderBic(config, login?.data?.token)})
                                if (result?.data?.result_code == '000') {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: "00",
                                        Message: result?.data?.result_desc,
                                        ...result?.data,
                                        Pr_key: `${result?.data?.bill_id}`
                                    });
                                } else {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: result?.data?.result_code,
                                        Message: result?.data?.result_desc
                                    });
                                }
                            }
                        }
                        return ChildRouter.responseSuccess("Ok", res, {});
                    }],
                },
            },
            '/get-loai-xe-bh': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1},

                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            data,
                            env
                        } = req.body;
                        const config = await getRemoteConfig(env)


                        if (hangBaoHiem === 'PVI') {
                            if (data?.ten_dmuc == 'PHAMVIMORONG') {
                                const arr = [
                                    {
                                        Text: 'Bảo hiểm mất cắp bộ phận',
                                        Value: '040003',
                                    },
                                    {
                                        Text: 'Không khấu hao phụ tùng vật tư thay mới',
                                        Value: '040006'
                                    },
                                    {
                                        Text: 'Bảo hiểm lựa chọn cơ sở chính hãng',
                                        Value: '040007'
                                    },
                                    {
                                        Text: 'Bảo hiểm thiệt hại động cơ do thủy kích',
                                        Value: '040008',
                                    }
                                ]
                                return ChildRouter.responseSuccess("Ok", res, {
                                    Status: "00",
                                    Message: "Ok",
                                    Data: arr
                                });
                            } else {
                                const result = await axios.post(config['API_URL_BH_PVI'] + 'ManagerApplication/Get_DanhMuc', data)
                                return ChildRouter.responseSuccess("Ok", res, {...result?.data});
                            }
                        }
                        if (hangBaoHiem === 'BIC') {
                            const login = await bicLogin(config)
                            if (data?.ten_dmuc == 'MDSD_AUTO' || data?.ten_dmuc == 'MDSD_VCXAUTO' || data?.ten_dmuc == 'TNDS_LOAIHINH_AUTO') {
                                const type = data?.ten_dmuc == 'MDSD_VCXAUTO' ? 'mv' : 'mvl'
                                const result = await axios.post(config['API_URL_BH_BIC'] + 'useslist', {
                                    "type": type,
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|' + type)

                                }, {headers: buildHeaderBic(config, login?.data?.token)})
                                const arr = result?.data?.data.map(e => {
                                    return {Text: e.value, Value: e.id + ''}
                                })
                                return ChildRouter.responseSuccess("Ok", res, {
                                    Status: result?.data?.result_code,
                                    Message: result?.data?.result_desc,
                                    Data: arr
                                });
                            }
                            // hãng xe oto
                            if (data?.ten_dmuc == 'HIEUXEAUTO') {
                                const result = await axios.post(config['API_URL_BH_BIC'] + 'vehiclemodel', {
                                    "type": "mvl",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mvl')

                                }, {headers: buildHeaderBic(config, login?.data?.token)})
                                const arr = result?.data?.data.map(e => {
                                    return {Text: e.value, Value: e.id + ''}
                                })
                                return ChildRouter.responseSuccess("Ok", res, {
                                    Status: result?.data?.result_code,
                                    Message: result?.data?.result_desc,
                                    Data: arr
                                });
                            }
                            // dòng xe
                            // if(data?.ten_dmuc == 'DONGXE')
                            // {
                            //     const result = await axios.post(config['API_URL_BH_BIC'] + 'vehiclemodel', {
                            //         "type":"mvl",
                            //         "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mvl')
                            //
                            //     }, { headers: buildHeaderBic(config, login?.data?.token)})
                            //     const arr = result?.data?.data.map(e => {
                            //         return { Text: e.value, Value: e.id + ''}
                            //     })
                            //     return ChildRouter.responseSuccess("Ok", res, {
                            //         Status: result?.data?.result_code,
                            //         Message: result?.data?.result_desc,
                            //         Data: arr});
                            // }
                            // loại xe máy
                            if (data?.ten_dmuc == 'LOAIXEMOTOR') {
                                const result = await axios.post(config['API_URL_BH_BIC'] + 'mc', {
                                    "type": "mc",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mc')

                                }, {headers: buildHeaderBic(config, login?.data?.token)})
                                const arr = Object.keys(result?.data?.data?.dungtichxilanh).map((key) => {
                                    return {Text: result?.data?.data?.dungtichxilanh[key], Value: key + ''}
                                })
                                return ChildRouter.responseSuccess("Ok", res, {
                                    Status: result?.data?.result_code,
                                    Message: result?.data?.result_desc,
                                    ...result?.data,
                                    Data: arr
                                });
                            }

                            if (data?.ten_dmuc == 'PHAMVIMORONG') {
                                const result = await axios.post(config['API_URL_BH_BIC'] + 'vehicleendor', {
                                    "type": "mvl",
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|mvl')

                                }, {headers: buildHeaderBic(config, login?.data?.token)})
                                const arr = result?.data?.data.map(e => {
                                    return {Text: e.value, Value: e.code + ''}
                                })
                                return ChildRouter.responseSuccess("Ok", res, {
                                    Status: result?.data?.result_code,
                                    Message: result?.data?.result_desc,
                                    Data: arr
                                });
                            }
                        }

                        return ChildRouter.responseError("Fail", res, {});
                    }],
                },
            },
            '/get-ma-loai-xe-bh': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1}
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            data,
                            env
                        } = req.body;

                        const config = await getRemoteConfig(env)

                        if (hangBaoHiem === 'PVI') {
                            const result = await axios.post(config['API_URL_BH_PVI'] + 'ManagerApplication/GetMaLoaiXe_Auto', data)
                            return ChildRouter.responseSuccess("Ok", res, {...result?.data});
                        }

                        if (hangBaoHiem === 'BIC') {
                            const login = await bicLogin(config)
                            if (data?.Ma_MDSD) {
                                const type = data?.type == 'tnds' ? 'mvl' : 'mv'
                                const result = await axios.post(config['API_URL_BH_BIC'] + 'vehiclelist', {
                                    "parent_id": data.Ma_MDSD,
                                    "type": type,
                                    "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|' + type + '|' + data.Ma_MDSD)

                                }, {headers: buildHeaderBic(config, login?.data?.token)})

                                if (result?.data?.result_code == '000') {
                                    if (type == 'mvl') {
                                        return ChildRouter.responseSuccess("Ok", res, {
                                            Status: "00",
                                            Message: result?.data?.result_desc,
                                            Data: result?.data?.data.mvl_type.map(e => {
                                                return {Text: e.value, Value: e.id + ''}
                                            })
                                        });
                                    } else if (type == 'mv') {
                                        return ChildRouter.responseSuccess("Ok", res, {
                                            Status: "00",
                                            Message: result?.data?.result_desc,
                                            Data: result?.data?.data.map(e => {
                                                return {Text: e.value, Value: e.id + '', ...e}
                                            })
                                        });
                                    }
                                } else {
                                    return ChildRouter.responseSuccess("Ok", res, {
                                        Status: result?.data?.result_code,
                                        Message: result?.data?.result_desc
                                    });
                                }
                            }
                        }

                        return ChildRouter.responseSuccess("Ok", res, {});
                    }],
                },
            },
            '/get-url-bh': {
                config: {
                    auth: [this.roles.all],
                    post: 'json',
                    validate: {
                        body: {
                            method: ['post'],
                            validate: [
                                {key: 'hangBaoHiem', type: this.dataType.string, name: 'hangBaoHiem', min: 1},
                                {key: 'data', type: this.dataType.string, name: 'data', min: 1}
                            ]
                        },
                    },
                },

                methods: {
                    post: [async function (req, res) {
                        let {
                            hangBaoHiem,
                            data,
                            env
                        } = req.body;

                        const config = await getRemoteConfig(env)

                        if (hangBaoHiem === 'PVI') {
                            const body = {
                                ...data,
                                CpId: config['PVI_CPID'],
                                Sign: md5(config['PVI_KEY'] + data?.RequestId),
                            }
                            const result = await axios.post(config['API_URL_BH_PVI'] + 'ManagerApplication/GetPolicyNumber', body)
                            return ChildRouter.responseSuccess("Ok", res, {...result?.data});
                        }


                        if (hangBaoHiem === 'BIC') {
                            const login = await bicLogin(config)
                            let type = ''
                            if (data?.Type == 'tnds') {
                                type = 'mvl'
                            } else if (data?.Type == 'vcx') {
                                type = 'mv'
                            } else if (data?.Type == 'tndsxm') {
                                type = 'mc'
                            }
                            const result = await axios.post(config['API_URL_BH_BIC'] + 'getbill', {
                                "bill_id": data?.RequestId,
                                "type": type,
                                "checksum": md5(config['BIC_PASSWORD'] + '|' + config['BIC_DEVICE_ID'] + '|' + type + '|' + data?.RequestId)

                            }, {headers: buildHeaderBic(config, login?.data?.token)})

                            return ChildRouter.responseSuccess("Ok", res, {
                                Status: result?.data?.result_code,
                                Message: result?.data?.result_desc,
                                ...result?.data,
                                URL: result?.data?.data?.certificate_file_url
                            });
                        }

                        return ChildRouter.responseSuccess("Ok", res, {});
                    }],
                },
            }
        }
    }
};
