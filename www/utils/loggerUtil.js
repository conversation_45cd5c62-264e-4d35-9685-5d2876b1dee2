"use strict";
// Tạ<PERSON> thời tắt Google Cloud Logging do thiếu quyền 'logging.logEntries.create'
// const {LoggingWinston} = require('@google-cloud/logging-winston');
const winston = require('winston');
exports.loggerUtil = winston.createLogger({
    level: 'info',
    transports: [
        new winston.transports.Console(),
        // Stackdriver Logging đã bị tắt tạm thời
        // Kích hoạt lại sau khi service account có quyền logging.logEntries.create
        // new LoggingWinston({
        //     projectId: 'lnbvd-75473',
        //     keyFilename: './lnbvd-75473-firebase-adminsdk-fbsvc-bb1510153f.json',
        // }),
    ],
});
