<style>
  .upload_img_product .upload_container .upfile input[type="file"] {
    position: absolute;
    opacity: 0;
    z-index: 999999999999999;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    visibility: unset;
  }
</style>
<div class="content_boxed">
  <section class="page_title">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <div class="page_title_container">
            <h1>
              <a href="/admin/quan-ly-don-hang.html"
                ><img src="/template/ui/img/arrow-left.png" alt=""
              /></a>
              Chi tiết đơn hàng
            </h1>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="sec_product">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <% if(Number(buyInfo.type) == 4){ %>
          <div class="status_order_product" style="background: #f44336">
            <span class="status_order">
              <PERSON><PERSON> thống sẽ kiểm tra các chứng cứ bạn gửi và phản hồi lại bạn sớm.
            </span>
            <span class="detail">
              Nếu có sai sót, bạn có thể nhấn hoàn thành đơn hàng khi người nhận
              đã nhận hàng
            </span>
          </div>
          <% }else{ %>
          <div
            class="status_order_product"
            style="background: <%- Number(buyInfo.type) == 0 ? '#08BCA6' : Number(buyInfo.type) == 1 ? '#00BCD4' : Number(buyInfo.type) == 2 ? '#2196F3' : Number(buyInfo.type) == 3 ? '#4CAF50' : '#9E9E9E' %>"
          >
            <span class="status_order">
              <img src="/template/ui/img/truck_w.svg" alt="" />
              <strong
                ><%- Number(buyInfo.status) == 0 ? 'Người mua đang chờ bạn xác
                nhận. Để xem thông tin người mua, bạn phải đồng ý bán hàng' : ''
                %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 1 ? 'Hãy gửi hàng qua các đơn vị
                vận chuyển và nhấn xác nhận đã gửi hàng để người mua theo dõi
                tiến độ đơn hàng. Lưu ý nếu hàng gửi đi mà bạn k nhấn đã gửi
                hàng thì người dùng có thể huỷ đơn hàng trên MP' : '' %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 2 ? 'Nếu người dùng đã nhận hàng,
                bạn có thể nhấn hoàn thành để kết thúc đơn hàng' : '' %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 3 ? 'Đơn hàng đã hoàn thành' : ''
                %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 4 ? 'Đơn hàng hoàn trả' : ''
                %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 5 ? 'Đơn hàng đã bị từ chối với
                nội dung: ' + buyInfo.messageReject : '' %></strong
              >
            </span>
          </div>
          <% } %>

          <div class="detail_order_container" style="margin-bottom: 18px">
            <div class="title_box">
              <h6>Ảnh trả hàng</h6>
            </div>
            <div class="content_box">
              <div class="row">
                <div class="col-md-9">
                  <div class="input_field upload_img_product schedule_field">
                    <div class="upload_container img_child">
                      <div
                        class="info_product_container_image"
                        style="flex-direction: row"
                      >
                        <% (buyInfo.imageConfirm || []).forEach((p, i)=>{ //
                        console.log('buyInfo', buyInfo) %>
                        <a
                          class="image-link"
                          href="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(p) %>"
                        >
                          <img
                            class="image-link"
                            style="
                              width: 100px;
                              height: 100px;
                              object-fit: cover;
                              margin-right: 10px;
                            "
                            src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(p) %>"
                            alt=""
                          />
                        </a>
                        <% }) %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="info_order_product">
            <div class="info_order_product_container">
              <div
                class="<%- [0, 5].includes(Number(buyInfo.type)) ? 'col_4' : 'col_3' %>"
              >
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/shopping-list.svg" alt="" />
                      Cửa hàng
                    </h6>
                  </div>
                  <div class="content_box">
                    <ul>
                      <li><%- buyInfo.storeName %></li>
                      <li><%- buyInfo.storeAddress %></li>
                    </ul>
                  </div>
                </div>
              </div>

              <div
                class="<%- [0, 5].includes(Number(buyInfo.type)) ? 'col_4' : 'col_3' %>"
              >
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/map.svg" alt="" /> Địa chỉ nhận
                      hàng
                    </h6>
                  </div>
                  <div class="content_box">
                    <ul>
                      <li><%- buyInfo.userBuyFullName %></li>
                      <li>
                        <%- buyInfo.type == 0 ? '********' : buyInfo.phone ||
                        'Không có số điện thoại' %>
                      </li>
                      <li>
                        <%- buyInfo.type == 0 ? '********' : buyInfo.address ||
                        'Không có địa chỉ' %>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <% if(![0, 5].includes(Number(buyInfo.type))){ %>
              <div class="col_3">
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/truck.svg" alt="" /> Thông tin
                      vận chuyển
                    </h6>
                  </div>
                  <div class="content_box">
                    <ul>
                      <li>Đơn vị: <%- buyInfo.shippingService %></li>
                      <li>
                        Mã vận đơn: <%- buyInfo.shippingCode ?
                        buyInfo.shippingCode : 'Chưa tạo vận đơn' %>
                      </li>
                      <li>Khối lượng : <%- buyInfo.totalWeight %> Gram</li>
                      <li>
                        Phí : <%- priceFormat(buyInfo.transportFee) %> VNĐ
                      </li>
                      <li>
                        Tình trạng : <%- buyInfo.shippingCode != '' ? '<span
                          class="badge badge-danger"
                          >Đã gửi đơn vị chuyển phát</span
                        >' : '<span class="badge badge-warning"
                          >Chưa tạo vận đơn</span
                        >' %>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <% } %>

              <div
                class="<%- [0, 5].includes(Number(buyInfo.type)) ? 'col_4' : 'col_3' %>"
              >
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/pay-per-click.svg" alt="" />
                      Phương thức thanh toán
                    </h6>
                  </div>
                  <div class="content_box">
                    <p>
                      <%- buyInfo.payments == 1 ? 'Chuyển khoản trước' : 'Thanh
                      toán khi nhận hàng' %>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="detail_order_container">
            <div class="title_box">
              <h6>Danh sách hàng mua</h6>
              <p class="code_product">Mã đơn hàng: <%- buyInfo.orderId %></p>
            </div>
            <% var TongTien = 0; buyInfo.products.forEach((p, i)=>{ TongTien +=
            Number(p.price) * Number(p.count); %>
            <div class="content_box">
              <div class="info_product_container">
                <div class="image_product">
                  <figure>
                    <img
                      src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(p.thumbnail) %>"
                      alt=""
                    />
                  </figure>
                </div>
                <div class="text_product">
                  <div class="name_product"><%- p.name %></div>
                  <div class="price_product">
                    <%- priceFormat(p.price) %> VNĐ
                  </div>
                  <ul class="list_detail_child">
                    <li>
                      <h6>Thương hiệu</h6>
                      <p><%- p.trademark %></p>
                    </li>
                    <% if (p.classifies && p.classifies.length > 0){ %> <%
                    p.classifies.forEach(item1=>{ %>
                    <li>
                      <h6><%- item1.name %></h6>
                      <p><%- item1.value %></p>
                    </li>
                    <% }) %> <% } %>
                    <li>
                      <h6>Số lượng</h6>
                      <p><%- p.count %></p>
                    </li>
                    <li>
                      <h6>Thành tiền</h6>
                      <p>
                        <%- priceFormat(Number(p.price) * Number(p.count)) %>
                        VND
                      </p>
                    </li>
                  </ul>
                  <p>
                    <strong style="color: red">Ghi chú sản phẩm:</strong> <%-
                    p.noteProduct %>
                  </p>
                </div>
              </div>
            </div>
            <% }) %>
            <div class="more text-right">
              <a href="javascript:;"
                >Tổng tiền hàng: <%- priceFormat(TongTien) %> VND</a
              >
              <a href="javascript:;">Giảm giá: -<%- giaTriGiam %></a>
              <a href="javascript:;"
                >Thành tiền: <% if(coupon.type == 1){ %> <%-
                priceFormat(TongTien - TongTien * coupon.value / 100, 2) %> VND
                + Phí ship <% }else{ %> <%- priceFormat(TongTien - coupon.value,
                2) %> VND + Phí ship <% } %>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
<script
  src="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/jquery.magnific-popup.min.js"
  integrity="sha512-IsNh5E3eYy3tr/JiX2Yx4vsCujtkhwl7SLqgnwLNgf04Hrt9BT9SXlLlZlWx+OK4ndzAoALhsMNcCmkggjZB1w=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/magnific-popup.min.css"
  integrity="sha512-+EoPw+Fiwh6eSeRK7zwIKG2MA8i3rV/DGa3tdttQGgWyatG/SkncT53KHQaS5Jh9MNOT3dmFL0FjTY08And/Cw=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>

<script>
  var buyInfo = <%- JSON.stringify(buyInfo) %>

      $(document).ready(function() {
          $('.image-link').magnificPopup({type:'image'});
      });
</script>
<script
  type="text/javascript"
  src="/template/admin/chi-tiet-don-hang.js?v=<%- cacheVersion %>"
></script>
