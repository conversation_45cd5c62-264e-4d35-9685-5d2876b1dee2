<div class="content_boxed">

    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><a href="/admin/quan-ly-banner-store.html"><img src="/template/ui/img/arrow-left.png" alt=""></a>
                            Chỉnh sửa Banner App
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <form id="form-edit-banner-app" onsubmit="return false">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Tiêu đề</label>
                                        <input type="text" name="name" value="<%- news.name %>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Sắp xếp</label>
                                        <input type="text" name="order" value="<%- news.order ? news.order : 0 %>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Tag</label>
                                        <input type="text" name="tag" value="<%- news.tag %>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="screen">Screen Type</label>
                                        <select name="screen" id="screen">
                                            <option value="LINK" <%- news.screen == 'LINK' ? 'selected' : '' %>>Mở đường dẫn Web</option>
                                            <option value="PROMOTION_DETAIL_SCREEN" <%- news.screen == 'PROMOTION_DETAIL_SCREEN' ? 'selected' : '' %>>Chương trình khuyến mại</option>
                                            <option value="SHOPPING_STACK" <%- news.screen == 'SHOPPING_STACK' ? 'selected' : '' %>>Màn shopping</option>
                                            <option value="PRODUCT_DETAILS" <%- news.screen == 'PRODUCT_DETAILS' ? 'selected' : '' %>>Chi tiết sản phẩm</option>
                                            <option value="BLOG_SCREEN" <%- news.screen == 'BLOG_SCREEN' ? 'selected' : '' %>>Màn danh sách tin tức</option>
                                            <option value="BLOG_DETAIL_SCREEN" <%- news.screen == 'BLOG_DETAIL_SCREEN' ? 'selected' : '' %>>Màn chi tiết bài viết</option>
                                            <option value="SERVICE_DETAIL" <%- news.screen == 'SERVICE_DETAIL' ? 'selected' : '' %>>Màn chi tiết thương hiệu</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="params">Link hoặc Param ID</label>
                                        <input type="text" id="params" name="params" value="<%- news.params %>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Tỉnh/Thành phố</label>
                                        <div class="select_form">
                                            <select id="city-state" class=""
                                                    name="province" <%- (typeof news != 'undefined') ? 'value="' + news.province + '"' : '' %>>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="input_field schedule_field upload_img_product">
                                        <label for="">Ảnh bìa</label>
                                        <div class="upload_container img_cover">
                                            <div class="upfile">
                                                <label for="upload-news-0">
                                                    <img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(news.thumbail) %>" alt=""></label>
                                                <input type="file" name="myfile_1" id="upload-news-0" accept="image/x-png,image/gif,image/jpeg"
                                                       class="upfile-input upload-new-picture">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="button_submit">
                                <a href="/admin/quan-ly-banner-store.html">Quay lại</a>
                                <button name="save">Lưu</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script>
    var news = <%- JSON.stringify(news) %>
</script>
<script type="text/javascript" src="/template/countries.js?v=<%- cacheVersion %>"></script>
<script type="text/javascript" src="/template/admin/chinh-sua-banner-store.js?v=<%- cacheVersion %>"></script>
