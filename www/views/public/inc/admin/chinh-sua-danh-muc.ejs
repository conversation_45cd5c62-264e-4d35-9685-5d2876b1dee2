<div class="content_boxed">

    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><a href="/admin/quan-ly-danh-muc.html"><img src="/template/ui/img/arrow-left.png"
                                                                        alt=""></a> Chi tiết danh mục
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio edit">
                        <form id="form-add-category">
                            <div class="input_container">
                                <div class="input_field search">
                                    <label for="">Tên danh mục</label>
                                    <input type="text" name="name" value="<%- category.name %>">
                                </div>
                                <div class="input_field portfolio_parent">
                                    <label for=""><PERSON>h mục cấp cha</label>
                                    <div class="select_form">
                                        <select class="portfolio_select" name="typeCategory">
                                            <option value="null">[[---------Chọn danh mục cha --------]]</option>
                                            <option <%- category.type == 0 ? 'selected' : '' %> value="0">Ô tô</option>
                                            <option <%- category.type == 1 ? 'selected' : '' %> value="1">Xe máy</option>
                                            <option <%- category.type == 2 ? 'selected' : '' %> value="2">Phương tiện khác</option>
                                            <option <%- category.type == 3 ? 'selected' : '' %> value="3">Dịch vụ</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_container">
                                        <div class="input_field schedule_field upload_img_product">
                                            <label for="">Ảnh bìa</label>
                                            <div class="upload_container img_cover">
                                                <div class="upfile">
                                                    <label for="upload-category-0"><img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(category.picture) %>"
                                                                                        alt=""></label>
                                                    <input class="upload-new-picture upfile-input" type="file"
                                                           name="myfile" id="upload-category-0" accept="image/x-png,image/gif,image/jpeg"
                                                           class="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="button_submit">
                                <a href="/admin/quan-ly-danh-muc.html">Quay lại</a>
                                <button name="save">Lưu</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

</div>
<script>
    var category = <%- JSON.stringify(category) %>
</script>
<script type="text/javascript" src="/template/admin/sua-danh-muc.js?v=<%- cacheVersion %>"></script>
