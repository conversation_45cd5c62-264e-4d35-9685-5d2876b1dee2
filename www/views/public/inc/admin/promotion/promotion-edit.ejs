<div class="content_boxed">
  <section class="page_title">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <div class="page_title_container">
            <h1>
              <a href="/admin/promotions.html"
                ><img src="/template/ui/img/arrow-left.png" alt=""
              /></a>
              Chỉnh sửa khuyến mãi
            </h1>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="sec_portfolio">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <div class="search_portfolio form_schedule edit">
            <form id="form-add-news" onsubmit="return false">
              <div class="row">
                <div class="col-md-12">
                  <div class="input_field schedule_field">
                    <label for="">Tiêu đề</label>
                    <input
                      type="text"
                      name="title"
                      value="<%- promotion.title %>"
                    />
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-6">
                  <div class="input_field schedule_field">
                    <label for="">Ngày bắt đầu</label>
                    <input
                      type="text"
                      name="startTime"
                      placeholder=""
                      value="<%- promotion.startTime %>"
                    />
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="input_field schedule_field">
                    <label for="">Ngày kết thúc</label>
                    <input
                      type="text"
                      name="endTime"
                      placeholder=""
                      value="<%- promotion.endTime %>"
                    />
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="input_field schedule_field">
                    <label for="">Thương hiệu</label>
                    <div class="select_form">
                      <select class="" name="stores" multiple="multiple">
                        <!--                                                <% brandList.forEach(item => { %>-->
                        <!--                                                    <% if (promotion.stores.includes(item._id.toString())){ %>-->
                        <!--                                                        <option value="<%- item._id %>" selected="selected">-->
                        <!--                                                            <%- item.name %>-->
                        <!--                                                        </option>-->
                        <!--                                                    <% } else { %>-->
                        <!--                                                        <option value="<%- item._id %>">-->
                        <!--                                                            <%- item.name %>-->
                        <!--                                                        </option>-->
                        <!--                                                    <% } %>-->
                        <!--                                                <% }) %>-->
                      </select>
                    </div>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="input_field schedule_field">
                    <label for="">Mô tả</label>
                    <textarea name="description" placeholder="mô tả">
<%- promotion.description %></textarea
                    >
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="input_field schedule_field">
                    <label for="">Nội dung</label>
                    <textarea name="" id="content-news">
<%- promotion.content %></textarea
                    >
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-12">
                  <div class="input_field schedule_field">
                    <label for="">Quy định cho Gara</label>
                    <textarea name="" id="content-term">
<%- promotion.contentTerm %></textarea
                    >
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-md-3">
                  <div class="input_field schedule_field upload_img_product">
                    <label for="">Ảnh đại diện</label>
                    <div class="upload_container img_cover">
                      <div class="upfile">
                        <label for="upload-news-0">
                          <button
                            class="delete-image"
                            style="
                              position: absolute;
                              left: 0;
                              top: 0;
                              width: 35px;
                              height: 25px;
                              padding: 5px;
                              font-size: 15px;
                              line-height: 5px;
                            "
                          >
                            Xóa
                          </button>
                          <img
                            src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(promotion.thumbnail) %>"
                            alt=""
                          />
                        </label>
                        <input
                          type="file"
                          name="thumbnail"
                          id="upload-news-0"
                          value="<%- promotion.thumbnail %>"
                          accept="image/x-png,image/gif,image/jpeg"
                          class="upfile-input upload-new-picture"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="input_field schedule_field upload_img_product">
                    <label for="">Ảnh bìa</label>
                    <div class="upload_container img_cover">
                      <div class="upfile">
                        <label for="upload-news-1">
                          <button
                            class="delete-image"
                            style="
                              position: absolute;
                              left: 0;
                              top: 0;
                              width: 35px;
                              height: 25px;
                              padding: 5px;
                              font-size: 15px;
                              line-height: 5px;
                            "
                          >
                            Xóa
                          </button>
                          <img
                            src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(promotion.cover) %>"
                            alt=""
                          />
                        </label>
                        <input
                          type="file"
                          name="cover"
                          id="upload-news-1"
                          value="<%- promotion.cover %>"
                          accept="image/x-png,image/gif,image/jpeg"
                          class="upfile-input upload-new-picture"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="button_submit">
                <a href="/admin/promotions.html">Quay lại</a>
                <button name="save">Lưu</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
<script>
  var news = <%- JSON.stringify(promotion) %>
  var brandList = <%- JSON.stringify(brandList) %>
  var selectedBrand = <%- JSON.stringify(promotion.stores) %>
  console.log(selectedBrand)
</script>
<script
  type="text/javascript"
  src="/template/admin/promotion-edit.js?v=<%- cacheVersion %>"
></script>
