<style>
  .upload_img_product .upload_container .upfile input[type="file"] {
    position: absolute;
    opacity: 0;
    z-index: 999999999999999;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    visibility: unset;
  }
</style>
<script>
  var feeShip = 0;
</script>
<div class="content_boxed">
  <section class="page_title">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <div class="page_title_container">
            <h1>
              <a href="/store/quan-ly-don-hang.html"
                ><img src="/template/ui/img/arrow-left.png" alt=""
              /></a>
              Chi tiết đơn hàng
            </h1>
            <div class="status">
              <% if(buyInfo.status == 0){ %>
              <span
                class="delivery_product"
                style="width: auto; cursor: pointer"
                onclick="dongYDonHang()"
                >Đồng ý bán hàng</span
              >
              <span
                class="no_process"
                style="width: auto; cursor: pointer"
                onclick="tuChoiDonHang()"
                >Từ chối đơn hàng</span
              >
              <% if(buyInfo.paymentMethod == 1 && buyInfo.isPayOnline == 1){
              }else{ %> <% } %> <% if(buyInfo.paymentMethod == 0 &&
              (buyInfo.isPayOnline == 0 || buyInfo.isPayOnline == null ||
              buyInfo.isPayOnline == 'null')){ }else{ %> <% } %> <% } %> <%
              if(buyInfo.status == 1){ %> <% if(!buyInfo.shippingCode){ %>
              <span
                class="delivery_product vdghtk"
                style="width: auto; cursor: pointer; background: #28a745"
                title="Giao hàng tiết kiệm về lấy hàng và vận chuyển"
                onclick="taoHoaDonGiaHang('ghtk')"
                >Tạo đơn vận GHTK</span
              >
              <% }else{ %>

              <span
                class="delivery_product"
                style="width: auto; cursor: pointer"
                title="Đơn vị vận chuyển đã lấy hàng"
                onclick="dvVcDaLayHang()"
                >Đơn vị vận chuyển đã lấy hàng</span
              >

              <span
                class="delivery_product"
                style="width: auto; cursor: pointer; background: #dc3545"
                title="Hủy giao hàng qua kênh hợp tác của MaxQ"
                onclick="huyGiaoHang()"
                >Hủy giao hàng</span
              >
              <% } %> <% } %> <% if(buyInfo.status == 2){ %>
              <span
                class="no_process"
                style="width: auto; cursor: pointer"
                onclick="donHangBiTraLaiViewBox()"
                >Đơn hàng bị trả lại</span
              >
              <span
                class="delivery_product"
                style="width: auto; cursor: pointer; background: #dc3545"
                title="Hủy giao hàng qua kênh hợp tác của maxQ"
                onclick="huyGiaoHang()"
                >Hủy giao hàng</span
              >
              <span
                class="delivery_product"
                style="width: auto; cursor: pointer"
                onclick="hoanThanhDonHang()"
                >Đã hoàn thành</span
              >
              <% } %>
              <!--                            <% if(buyInfo.status == 4){ %>-->
              <!--                                <span class="delivery_product" style="width: auto; cursor: pointer"-->
              <!--                                      onclick="hoanThanhDonHang()">Đã hoàn thành</span>-->
              <!--                            <% } %>-->
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section class="sec_product">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <% if(Number(buyInfo.status) == 4){ %>
          <div class="status_order_product" style="background: #f44336">
            <span class="status_order">
              Hệ thống sẽ kiểm tra các chứng cứ bạn gửi và phản hồi lại bạn sớm.
            </span>
            <span class="detail">
              Nếu có sai sót, bạn có thể nhấn hoàn thành đơn hàng khi người nhận
              đã nhận hàng
            </span>
          </div>
          <% }else{ %>
          <div
            class="status_order_product"
            style="background: <%- Number(buyInfo.status) == 0 ? '#08BCA6' : Number(buyInfo.status) == 1 ? '#00BCD4' : Number(buyInfo.status) == 2 ? '#2196F3' : Number(buyInfo.status) == 3 ? '#4CAF50' : '#9E9E9E' %>"
          >
            <span class="status_order">
              <img src="/template/ui/img/truck_w.svg" alt="" />
              <strong
                ><%- Number(buyInfo.status) == 0 ? 'Người mua đang chờ bạn xác
                nhận. Để xem thông tin người mua, bạn phải đồng ý bán hàng' : ''
                %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 1 ? 'Hãy gửi hàng qua các đơn vị
                vận chuyển và nhấn xác nhận đã gửi hàng để người mua theo dõi
                tiến độ đơn hàng. Lưu ý nếu hàng gửi đi mà bạn k nhấn đã gửi
                hàng thì người dùng có thể huỷ đơn hàng' : '' %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 2 ? 'Nếu người dùng đã nhận hàng,
                bạn có thể nhấn hoàn thành để kết thúc đơn hàng' : '' %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 3 ? 'Đơn hàng đã hoàn thành' : ''
                %></strong
              >
              <strong
                ><%- Number(buyInfo.status) == 5 ? 'Đơn hàng đã bị từ chối với
                nội dung: ' + buyInfo.messageReject : '' %></strong
              >
            </span>
          </div>
          <% } %>

          <div class="info_order_product">
            <div class="info_order_product_container">
              <div
                class="<%- [0, 5].includes(Number(buyInfo.status)) ? 'col_4' : 'col_3' %>"
              >
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/shopping-list.svg" alt="" />
                      Cửa hàng
                    </h6>
                  </div>
                  <div class="content_box">
                    <ul>
                      <li><%- buyInfo.storeName %></li>
                      <li><%- buyInfo.storeAddress %></li>
                    </ul>
                    <div class="mt-3">
                      <strong
                        ><img src="/template/ui/img/map.svg" alt="" /> Chọn 1
                        địa chỉ đến lấy hàng trước khi tạo mã vận đơn</strong
                      >
                      <div class="mt-2">
                        <form id="select-address-pickup">
                          <% branches.forEach(item=>{ console.log('branch item
                          ', item) %>
                          <div style="display: flex; align-items: center">
                            <input
                              type="radio"
                              id="<%- item._id %>"
                              name="branch"
                              value="<%- item._id %>"
                            />
                            <label
                              style="font-weight: normal; margin-left: 5px"
                              for="<%- item._id %>"
                              ><%- item.name %> - SĐT: <%- item.phone %> - <%-
                              item.address %></label
                            >
                          </div>
                          <% }) %>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="<%- [0, 5].includes(Number(buyInfo.status)) ? 'col_4' : 'col_3' %>"
              >
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/map.svg" alt="" /> Địa chỉ nhận
                      hàng
                    </h6>
                  </div>
                  <div class="content_box">
                    <ul>
                      <li>
                        <%- buyInfo.userBuyFullName %> <% if(buyInfo.status !=
                        0){ %>
                        <a
                          href="/store/quan-ly-tin-nhan.html?userId=<%- buyInfo.userId %>"
                          class="chat"
                          >Chat ngay</a
                        >
                        <% } %>
                      </li>
                      <li>
                        <%- buyInfo.status == 0 ? '********' : buyInfo.phone ||
                        'Không có số điện thoại' %>
                      </li>
                      <li>
                        <%- buyInfo.status == 0 ? '********' : buyInfo.address
                        || 'Không có địa chỉ' %>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <% if(![0, 5].includes(Number(buyInfo.status))){ %>
              <div class="col_3">
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/truck.svg" alt="" /> Thông tin
                      vận chuyển
                    </h6>
                    <!--                                            <a target="_blank" href="/store/in-nhan-van-chuyen.html/<%- buyInfo._id %>">In nhãn vc</a>-->
                  </div>
                  <div class="content_box">
                    <ul>
                      <li>Đơn vị: <%- buyInfo.shippingService %></li>
                      <li>
                        Mã vận đơn: <%- buyInfo.shippingCode ?
                        buyInfo.shippingCode : 'Chưa tạo vận đơn' %>
                      </li>
                      <li>Khối lượng : <%- buyInfo.totalWeight %> Gram</li>
                      <li>
                        Phí : <%- priceFormat(buyInfo.transportFee) %> VNĐ
                      </li>
                      <li>
                        Tình trạng : <%- buyInfo.shippingCode != '' ? '<span
                          class="badge badge-danger"
                          >Đã gửi đơn vị chuyển phát</span
                        >' : '<span class="badge badge-warning"
                          >Chưa tạo vận đơn</span
                        >' %>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <% } %>

              <div
                class="<%- [0, 5].includes(Number(buyInfo.status)) ? 'col_4' : 'col_3' %>"
              >
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>
                      <img src="/template/ui/img/pay-per-click.svg" alt="" />
                      Phương thức thanh toán
                    </h6>
                  </div>
                  <div class="content_box">
                    <p>
                      <%- buyInfo.paymentMethod == 0 ? 'COD' :
                      buyInfo.paymentMethod == 1 ? 'Thanh toán Online' :
                      buyInfo.paymentMethod == 3 ? 'Điểm thưởng' : 'Chưa rõ' %>
                      <%- buyInfo.isPayOnline == 1 ? '
                      <b style="color: green">Đã thanh toán </b>' : ' -
                      <b style="color: red">Chưa thanh toán</b>' %>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-8">
              <div class="detail_order_container info_order_product">
                <div class="title_box">
                  <h6>Danh sách hàng mua</h6>
                  <p class="code_product">
                    Mã đơn hàng: <%- buyInfo.orderId %>
                  </p>
                </div>
                <% var TongTien = Number(buyInfo.totalPriceShop); let
                TongThanhToan = Number(buyInfo.totalPriceShop) +
                Number(buyInfo.transportFee); buyInfo.products.forEach((p, i)=>{
                // console.log('buyInfo', buyInfo) %>
                <div class="content_box">
                  <div class="info_product_container">
                    <div class="image_product">
                      <figure>
                        <img
                          src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(p.thumbnail) %>"
                          alt=""
                        />
                      </figure>
                    </div>
                    <div class="text_product">
                      <div class="name_product"><%- p.productName %></div>
                      <% if (p.classifyActive && p.classifyActive.length > 0){
                      %> <% p.classifyActive.forEach(item1=>{ %>
                      <div class="price_product">
                        <%- priceFormat(getNumberPrice(item1.price)) %> VNĐ
                      </div>
                      <% }) %> <% } else{ %>
                      <div class="price_product">
                        <%- priceFormat(p.price) %> VNĐ
                      </div>
                      <% } %>

                      <ul class="list_detail_child">
                        <li>
                          <h6>Thương hiệu</h6>
                          <p><%- p.trademark %></p>
                        </li>
                        <% if (p.classifyActive && p.classifyActive.length > 0){
                        %> <% p.classifyActive.forEach(item1=>{ %>
                        <li>
                          <h6><%- item1.name %></h6>
                          <p><%- item1.value %></p>
                        </li>
                        <li>
                          <h6>Khối lượng VC</h6>
                          <p><%- item1.mass %> (Kg)</p>
                        </li>
                        <% }) %> <% } else { %>
                        <li>
                          <h6>Khối lượng VC</h6>
                          <p><%- p.weight %> (Kg)</p>
                        </li>
                        <% } %>
                        <li>
                          <h6>Số lượng</h6>
                          <p><%- p.count %></p>
                        </li>
                        <li>
                          <h6>Thành tiền</h6>
                          <% if (p.classifyActive && p.classifyActive.length >
                          0){ %> <% p.classifyActive.forEach(item1=>{ %>
                          <p>
                            <%- priceFormat(Number(getNumberPrice(item1.price))
                            * Number(p.count)) %> VNĐ
                          </p>
                          <% }) %> <% } else{ %>
                          <p>
                            <%- priceFormat(Number(p.price) * Number(p.count))
                            %> VNĐ
                          </p>
                          <% } %>
                        </li>
                      </ul>
                      <p>
                        <strong style="color: red">Ghi chú sản phẩm:</strong>
                        <%- p.noteProduct %>
                      </p>
                    </div>
                  </div>
                </div>
                <% }) %>
              </div>
            </div>
            <div class="col-md-4">
              <div class="info_order_product">
                <div class="box_info_order">
                  <div class="title_box">
                    <h6>Chi phí</h6>
                  </div>
                  <div class="content_box">
                    <ul class="order-list-price">
                      <li>
                        <label>Mã giảm giá maxQ:</label>
                        <span
                          ><%- buyInfo.gCoupon ? buyInfo.gCoupon : 'Không sử
                          dụng' %></span
                        >
                      </li>
                      <li>
                        <label>Mã giảm giá shop:</label>
                        <span
                          ><%- buyInfo.coupon ? buyInfo.coupon : 'Không sử dụng'
                          %></span
                        >
                      </li>
                      <li>
                        <label for="">Giảm giá:</label> <%- buyInfo.coupon.value
                        ? numberFormat(buyInfo.coupon.value) : '0' %> VND
                      </li>
                      <li>
                        <label for="">Tổng tiền hàng:</label> <%-
                        priceFormat(TongTien) %> VND
                      </li>
                      <li>
                        <label for="">Phí vận chuyển:</label> <%-
                        buyInfo.transportFee == 0 ? 'Miễn phí' :
                        (numberFormat(buyInfo.transportFee)) %> VND
                      </li>
                      <li>
                        <label for="">Thành tiền:</label> <%-
                        numberFormat(TongThanhToan) %> VND
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-8">
              <div class="detail_order_container info_order_product">
                <div class="title_box">
                  <h6>Hình ảnh hoàn trả hàng</h6>
                </div>
                <div class="content_box">
                  <div
                    class="info_product_container_image"
                    style="flex-direction: row"
                  >
                    <% (buyInfo.imageConfirm || []).forEach((p, i)=>{ //
                    console.log('buyInfo', buyInfo) %>
                    <a
                      class="image-link"
                      href="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(p) %>"
                    >
                      <img
                        class="image-link"
                        style="
                          width: 100px;
                          height: 100px;
                          object-fit: cover;
                          margin-right: 10px;
                        "
                        src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(p) %>"
                        alt=""
                      />
                    </a>
                    <% }) %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<div class="popup" id="tuChoiDonHang">
  <div class="popup_container popup_detail_product">
    <div class="title_popup">
      Lý do từ chối đơn hàng
      <span class="close_popup"
        ><img src="/template/ui/img/close.svg" alt=""
      /></span>
    </div>
    <div class="content_popup">
      <div class="info_confirm">
        <div class="text_info_confirm">
          <p>
            Để giúp nguời dùng cải thiện thao tác đặt hàng hoặc thay đổi đơn
            hàng phù hợp với tiêu chí của của hàng
          </p>
        </div>
        <div class="icon"><img src="/template/ui/img/check.svg" alt="" /></div>
      </div>

      <div class="info_confirm">
        <textarea
          id="lyDoTuChoiDonHang"
          style="width: 100%; height: 100px"
        ></textarea>
      </div>
    </div>
    <div class="submit_popup text-right">
      <a href="javascript:;" onclick="guiLenhTuChoi()" class="confirm"
        >Từ chối đơn hàng</a
      >
    </div>
  </div>
</div>

<div class="popup" id="donHangBiTraLaiViewBox">
  <div class="popup_container popup_detail_product">
    <div class="title_popup">
      Vui lòng cung cấp 3 ảnh chụp sản phậm bị trả lại<br />
      (có dấu đỏ hoặc biên lai trả hàng)
      <span class="close_popup"
        ><img src="/template/ui/img/close.svg" alt=""
      /></span>
    </div>
    <div class="content_popup input_field upload_img_product schedule_field">
      <div class="upload_container img_child" style="padding: 22px">
        <div class="upfile">
          <label for="upload-product-1"
            ><img src="/template/ui/img/upload.png" alt=""
          /></label>
          <input
            class="upload-new-picture upfile-input"
            type="file"
            name="myfile_1"
            id="upload-image-1"
            accept="image/x-png,image/gif,image/jpeg"
          />
        </div>
        <div class="upfile">
          <label for="upload-product-2"
            ><img src="/template/ui/img/upload.png" alt=""
          /></label>
          <input
            class="upload-new-picture upfile-input"
            type="file"
            name="myfile_2"
            id="upload-image-2"
            accept="image/x-png,image/gif,image/jpeg"
          />
        </div>
        <div class="upfile">
          <label for="upload-product-3"
            ><img src="/template/ui/img/upload.png" alt=""
          /></label>
          <input
            class="upload-new-picture upfile-input"
            type="file"
            name="myfile_3"
            id="upload-image-3"
            accept="image/x-png,image/gif,image/jpeg"
          />
        </div>
      </div>
    </div>
    <div class="submit_popup text-right">
      <a href="javascript:;" onclick="guiAdminreview()" class="confirm"
        >Gửi ADMIM kiểm tra</a
      >
    </div>
  </div>
</div>
<script
  src="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/jquery.magnific-popup.min.js"
  integrity="sha512-IsNh5E3eYy3tr/JiX2Yx4vsCujtkhwl7SLqgnwLNgf04Hrt9BT9SXlLlZlWx+OK4ndzAoALhsMNcCmkggjZB1w=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>
<link
  rel="stylesheet"
  href="https://cdnjs.cloudflare.com/ajax/libs/magnific-popup.js/1.1.0/magnific-popup.min.css"
  integrity="sha512-+EoPw+Fiwh6eSeRK7zwIKG2MA8i3rV/DGa3tdttQGgWyatG/SkncT53KHQaS5Jh9MNOT3dmFL0FjTY08And/Cw=="
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
/>
<script>
  var buyInfo = <%- JSON.stringify(buyInfo) %>
  console.log(buyInfo)
  var transportFee = <%- JSON.stringify(buyInfo.transportFee) %>
  var branches = <%- JSON.stringify(branches) %>
      console.log(branches)

  $(document).ready(function() {
      $('.image-link').magnificPopup({type:'image'});
  });
</script>
<script
  type="text/javascript"
  src="/template/store/chi-tiet-don-hang.js?v=<%- cacheVersion %>"
></script>
