<style>
    .upfile label::before {
        content: unset !important;
    }
</style>
<div class="content_boxed">

    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1>
                            <a onclick="goBack()">
                                <img src="/template/ui/img/arrow-left.png" /></a> Chỉnh sửa <%- classifyServiceType %>
                        </h1>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <form id="form-add-product" onsubmit="return false">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Tên dịch vụ</label>
                                        <input type="text" name="name" value="<%- service.name %>">
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="input_field schedule_field">
                                        <label for="">Thương hiệu</label>
                                        <div class="select_form">
                                            <select class="" name="storeId">
                                                <option value="null">[[---------Chọn thương hiệu --------]]</option>
                                                <% brands.forEach(item=>{ %>
                                                    <option <%- service.storeId == item._id ? 'selected' : '' %>
                                                            value="<%- item._id %>"><%- item.name %> </option>
                                                <% }) %>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Cơ sở</label>
                                        <div class="select_form">
                                            <div class="select_form">
                                                <select class="" name="branchId" multiple="multiple">
                                                    <% danhsachCoSo.forEach(item =>{ %>
                                                        <option <%- service.branchId.includes(item._id)  ? 'selected="selected"' : '' %> value="<%- item._id %>">
                                                            <%- item.name %>
                                                        </option>
                                                    <% }) %>
                                                </select>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Mô tả ngắn</label>
                                        <textarea name="shortDes" placeholder="Nhập nội dung" style="height: 100px"><%- service.shortDes %></textarea>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="input_field schedule_field">
                                        <label for="">Mô tả</label>
                                        <textarea name="description" placeholder="Nhập nội dung"><%- service.description %></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Giá</label>
                                        <input name="price" placeholder="Nhập giá" type="text"
                                               data-type="currency" value="<%- priceFormat(service.price) %> VND">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input_field schedule_field">
                                        <label for="">Tình trạng</label>
                                        <div class="select_form">
                                            <select class="" name="typeService">
                                                <option value="all" disabled>Chọn tình trạng</option>
                                                <option <%- service.typeService == 0 ? 'selected' : '' %> value="0">Tạm ngừng
                                                </option>
                                                <option <%- service.typeService == 1 ? 'selected' : '' %> value="1">sẵn sàng
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="classify-list">
                                <% service.classify.forEach((item1, index)=>{ %>
                                    <div class="col-md-12 classify-item" id="classify-item-${count+1}">
                                        <div class="input_field schedule_field">
                                            <label for="" class="title-classify">Nhóm <%- index + 1 %></label>
                                            <div class="row type-product">
                                                <label for="">Tên nhóm</label>
                                                <div class="col-md-12">
                                                    <div class="input_field schedule_field">
                                                        <input placeholder="Tên nhóm con" name="name-type"
                                                               type="text" value="<%- item1.name %>">
                                                    </div>
                                                </div>
                                                <label for="">Phân loại dịch vụ</label>
                                                <div style="width: 100%;" class="type-list">
                                                    <% for ( let i = 0; i < item1.data.length; i++ ) { %>
                                                        <div class="col-md-12 type-item">
                                                            <div class="input_field schedule_field">
                                                                <input name="nameProd" placeholder="Nhập phân loại dịch vụ" type="text" class="mr-3" value="<%- item1.data[i].name %>">
                                                                <input name="sellingPrice" placeholder="Nhập giá" type="text" data-type="currency" value="<%- item1.data[i].price %>">
                                                                <a href="javascript:;" class="delete-service"
                                                                   title="Xoá" onclick="deleteTypeItem(this)">
                                                                    <img src="/template/ui/img/delete-2-bage.svg"
                                                                         alt="">
                                                                </a>
                                                            </div>
                                                        </div>
                                                    <% } %>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <a class="button-classify"
                                                           style="background: white;border: solid 2px #8db7ff; color: #8db7ff;"
                                                           onclick="addType(this)"><img
                                                                    src="/template/ui/img/plus-nhat.svg" alt="">
                                                            Thêm</a>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <a class="button-classify"
                                                           style="background: white;border: solid 2px #FF1615; color: #FF1615;"
                                                           onclick="deleteClassify(this)"><img
                                                                    src="/template/ui/img/delete-2-bage-red.svg" alt="">
                                                            Xóa</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <% }) %>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                </div>
                                <div class="col-md-4">
                                    <br>
                                    <br>
                                    <div class="input_field schedule_field">
                                        <a class="button-classify"
                                           style="background: white;border: solid 2px #1969EF; color: #1969EF;"
                                           onclick="addClassify()"><img src="/template/ui/img/plus-blue.svg" alt="">
                                            Thêm phân loại</a>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3">
                                    <%

                                    let image = service.thumbail;
                                    const arr_str = image.split("files/");
                                    image = 'files/' + arr_str[1];

                                    %>
                                    <div class="input_field schedule_field upload_img_product">
                                        <label for="">Ảnh bìa</label>
                                        <div class="upload_container img_cover">
                                            <div class="upfile">
                                                <label for="upload-product-0">
                                                    <button class="delete-image"  style="position: absolute; left:0 ;top: 0;width: 35px;height: 25px;padding: 5px;font-size: 15px;line-height: 5px;">Xóa</button>
                                                    <img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(image) %>" alt=""></label>
                                                <input type="file" name="myfile_1" value="<%- service.thumbail %>" id="upload-product-0" accept="image/x-png,image/gif,image/jpeg" class="upfile-input upload-new-picture">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <section>
                            <div id="dropzone">
                                <form class="dropzone needsclick" id="upload-images" max-files="4" action="/">
                                    <div class="dz-message needsclick">
                                        Kéo ảnh thả ảnh vào hoặc<span
                                                class="btn btn-link">ấn vào đây để tải ảnh</span></span>.
                                        <span class="note needsclick">( Kích thước tối đa <strong>3MB</strong>)</span>
                                    </div>
                                </form>
                            </div>
                        </section>
                        <div class="button_submit button_submit_form">
                            <a onclick="goBack()">Quay lại</a>
                            <button onclick="submitForm()" name="save">Lưu</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

</div>
<script>
    function goBack() {
        window.history.back();
    }
    let pictureOlds = <%- JSON.stringify(service.pictures) %>;
    let serviceId = '<%- service._id %>';
    let storeId = '<%- service.storeId %>';
    let branchIds = '<%- service.branchId %>';
    let branchId = '<%- service.branchId %>';
</script>
<script>
    var classifyServiceType = '<%- classifyServiceType %>';
</script>
<script type="text/javascript" src="/template/store/edit-service-of-brand.js?v=<%- cacheVersion %>"></script>
