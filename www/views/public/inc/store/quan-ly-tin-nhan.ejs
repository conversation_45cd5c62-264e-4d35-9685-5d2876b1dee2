<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1><PERSON><PERSON><PERSON>n lý tin nhắn</h1>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="sec_message">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="message_container">
                        <div class="box_chat">
                            <div class="title_box">
                                <img src="template/default-avatar.jpg" alt="">
                            </div>
                            <div class="content_box">
                                <div class="content_chat">
                                    <ul>

                                    </ul>
                                </div>
                                <div class="form_enter_chat d-block">
                                    <form action="#" method="post" onsubmit="return false">
                                        <input style="width: calc(100% - 50px);" type="text" id="send-message"
                                               name="enterchatcontent" placeholder="Nhập tin nhắn"
                                               data-emojiable="true">
<!--                                        <label for="imgupload" class="send_enter_chat"-->
<!--                                               style="right: 50px;line-height: 50px;">-->
<!--                                            <img src="/template/ui/img/image-icon.png" alt="images"-->
<!--                                                 style="max-width: 34px;">-->
                                        </label>
<!--                                        <input multiple type="file" name="" id="imgupload" class="inputfile"-->
<!--                                               accept="image/*" style="display: none">-->
                                        <button type="submit" class="send_enter_chat"><img
                                                    src="/template/ui/img/send.svg" alt="images"></button>
                                    </form>
                                </div>

                                <div class="form_disable_chat">
                                    <p>Người dùng này đã tắt nhận tin nhắn</p>
                                </div>
                            </div>
                        </div>
                        <div class="list_chat_item">
<!--                            <form class="search_message" action="javascript:;" method="post">-->
<!--                                <div class="input_field">-->
<!--                                    <input type="text" id="search-message" name="search_message"-->
<!--                                           placeholder="Tìm kiếm tin nhắn">-->
<!--                                    <button type="submit" name="search_message"><img src="/template/ui/img/search.svg"-->
<!--                                                                                     alt=""></button>-->
<!--                                </div>-->
<!--                            </form>-->
                            <div class="list_chat_container">
                                <ul>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
    var userChatId = '<%- userChatId %>';
    var userLoginId = '<%- userLoginId %>';
</script>
<script src="https://cdn.jsdelivr.net/npm/lodash@4.17.10/lodash.min.js"></script>
<script type="module">
    // Import the functions you need from the SDKs you need
    import { initializeApp } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-app.js";
    import { getAnalytics } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-analytics.js";
    import { getFirestore, collection, addDoc, doc, onSnapshot, query, getDocs, where, getDoc, setDoc, updateDoc } from "https://www.gstatic.com/firebasejs/9.6.10/firebase-firestore.js";
    // TODO: Add SDKs for Firebase products that you want to use
    // https://firebase.google.com/docs/web/setup#available-libraries

    // Your web app's Firebase configuration
    // For Firebase JS SDK v7.20.0 and later, measurementId is optional
    const firebaseConfig = {
        apiKey: "AIzaSyCeYUnODV7lrPa7bTAmyGjzebneFCQsFXQ",
        authDomain: "lnbvd-75473.firebaseapp.com",
        databaseURL: "https://lnbvd-75473-default-rtdb.asia-southeast1.firebasedatabase.app",
        projectId: "lnbvd-75473",
        storageBucket: "lnbvd-75473.firebasestorage.app",
        messagingSenderId: "139958195942",
        appId: "1:139958195942:web:fc66beb47ae8b627034d27",
        measurementId: "G-QYCP9DY58R"
    };

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    const analytics = getAnalytics(app);
    const db = getFirestore(app);
    // try {
    //     const docRef = await addDoc(collection(db, "users"), {
    //         first: "Ada",
    //         last: "Lovelace",
    //         born: 1815
    //     });
    //     console.log("Document written with ID: ", docRef.id);
    // } catch (e) {
    //     console.error("Error adding document: ", e);
    // }

    const q = query(collection(db, "chatRooms"))
    // const q = query(collection(db, "chatRooms", where('members', 'array-contains', userLoginId)))

    const querySnapshot = await getDocs(q);
    const threads = querySnapshot.docs.map(documentSnapshot => {
        if (documentSnapshot.data() && documentSnapshot.data().members && documentSnapshot.data().members.length && documentSnapshot.data().members.includes(userLoginId)) {
            return {
                _id: documentSnapshot.id,
                // give defaults
                name: '',
                latestMessage: {
                    text: ''
                },
                ...documentSnapshot.data()
            }
        }
        return null
    })
    console.log('userChatId', userChatId)
    console.log('threads', threads)
    const rooms = _.orderBy(threads.filter(x => x !== null && x?.latestMessage?.text?.length > 0), item => item.latestMessage.createdAt, ['desc'])
    console.log('rooms', rooms)

    const roomId = getParameterByName("roomId");
    const userId = getParameterByName("userId"); // case bấm chat với user từ chủ shop

    let room = null;
    let roomRef = null
    if (roomId)
    {
        const collectionRef = collection(db, "chatRooms", roomId, "messages");
        roomRef = doc(db, "chatRooms", roomId)
        const rsGetRoom = await getDoc(roomRef)
        // , where('members', 'array-contains', userLoginId)
        if (rsGetRoom.exists()) {
            console.log('room', rsGetRoom.data()) // roomDetail
            room = rsGetRoom.data();
        }
        else {
            console.log("No such document")
        }

        onSnapshot(collectionRef, (querySnapshot) => {
            const threads = querySnapshot.docs.map(doc => {
                const firebaseData = doc.data()
                const data = {
                    _id: doc.id,
                    text: '',
                    createdAt: new Date().getTime(),
                    ...firebaseData
                }

                if (!firebaseData.system) {
                    data.user = {
                        ...firebaseData.user,
                        // name: firebaseData.user.name
                    }
                }

                return data
            })

            const messages = _.orderBy(threads, item => item.createdAt, ['asc'])

            $('.title_box').html(`
<!--		<img src="${room.userPicture ? room.userPicture : ''}" alt=""> -->
            ${room?.fromName ? room?.fromName : ''}
		`)

            let messageShows = []
            messages.forEach(item => {
                // if (item.userSendId == userSelect.userId || item.userId == userSelect.userId) {
                //     messageShows.push(item)
                // }
                item['userId'] = item.user._id
                item['avatar'] = room["avatar_" + item.user._id]
                console.log(item)
                messageShows.push(item)
            })
            messageShows.sort((a, b) => (a.createAt > b.createAt) ? 1 : ((b.createAt > a.createAt) ? -1 : 0));
            let html = ''
            messageShows.forEach(item => {
                let content = ''
                let avatar = ''
                if (item.avatar)
                {
                    avatar = `<div class="img_chat"><img style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover" src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${item.avatar}" alt=""></div>`
                }
                if (item.type == 0) {
                    content = `<p>${item.text}</p>`
                } else if (item.type == 1) {
                    content = `<img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${item.picture}" alt="" style="width: ${findSizeImg(item).width}px; height: ${findSizeImg(item).height}px; object-fit: contain ">`
                } else {
                    content = item.text
                }
                html += `
			<li data-login-id="${userLogin._id}" data-user-id="${item.userId}" class="text-left ${item.userId != userLogin._id ? 'customer' : 'host'}">
				${avatar}
				<div class="text_chat" style="background: ${item.type == 1 ? 'none' : 'null'}">
					${content}
				</div>
				<div class="time_chat">
					${moment(item.createdAt).locale('vi').fromNow()}
				</div>
			</li>
			`
            })
            $('.content_chat ul').html(html)
            $(".content_chat").animate({scrollTop: $('.content_chat').prop("scrollHeight")}, 500);
        });

    } else if (userId)
    {
        const room = rooms.find(x => x.roomId.indexOf(userId) !== -1)
        debugger
        if (room)
        {
            window.location = window.location.pathname + '?roomId=' + room._id
        } else {
            // TODO: tạo roomChat mới
            alert('Bạn chỉ có thể chat với tài khoản người dùng đã gủi tin nhắn cho bạn !')
        }
    } else {
        alert('Không tìm thấy roomId hoặc userId')
    }

    renderListUsers()

    function renderListUsers() {
        let html = ''
        rooms.forEach((item, index) => {
            let content = ''
            if (item.type == 0) {
                content = `${item.content}`
            } else if (item.type == 1) {
                content = 'Đã gửi 1 hình ảnh'
            }
            let displayUserId = item.fromId == userLogin._id ? item.toUserId : item.fromId
            const displayName = item['displayName_' + displayUserId]
            let avatarDisplay = item["avatar_" + displayUserId]
            html += `
			<li class="chat_item">
				<a href="/store/quan-ly-tin-nhan.html?roomId=${item._id}" class="${ roomId == item._id ? 'active' : ''}">
					<div class="img">
						<img style="width: 40px;height: 40px;border-radius: 50%;" src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/${avatarDisplay}" alt="">
					</div>
					<div class="content_chat_item">
						<div class="name_chat">
							<div class="name">
								${displayName}
							</div>
							<span class="time_chat">${moment(item.latestMessage.createdAt).locale('vi').fromNow()}</span>
						</div>
						<div class="text_chat">
							${content}
						</div>
					</div>
				</a>
			</li>
			`
        })
        $('.list_chat_container ul').html(html)
        // filterListUsers()
    }

    function getParameterByName(name, url = window.location.href) {
        name = name.replace(/[\[\]]/g, '\\$&');
        var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
            results = regex.exec(url);
        if (!results) return null;
        if (!results[2]) return '';
        return decodeURIComponent(results[2].replace(/\+/g, ' '));
    }

    async function sendChatMessage() {
        if ($('.emoji-wysiwyg-editor').html().toString().trim().length > 0) {
            const roomId = getParameterByName("roomId")
            const collectionRef = collection(db, "chatRooms", roomId, "messages");
            const text = $('.emoji-wysiwyg-editor').html().toString().trim()
            const docRef = await addDoc(collectionRef, {
                text: text,
                createdAt: new Date().getTime(),
                user: {
                    _id: userLogin._id,
                    name: userLogin.fullName || ''
                },
                read: false
            });

            console.log("Document written with ID: ", docRef.id);
            console.log("User Login", userLogin);
            $('#send-message').val('')
            $('.emoji-wysiwyg-editor').html('')

            // update Room
            let roomUpdate = {
                latestMessage: {
                    text,
                    createdAt: new Date().getTime()
                }
            }

            // cập nhật lại avatar
            if (userLogin && userLogin.picture)
            {
                roomUpdate['avatar_' + userLogin._id] = userLogin.picture
                roomUpdate['displayName_' + userLogin._id] = userLogin.fullName
            }

            if (room) {
                setDoc(roomRef, roomUpdate, { merge: true });
                if (room.toUserId && userLogin._id)
                {
                    // push thông báo
                    post(`/user/api/push-fcm-notification`, { toUserId: room.toUserId, fromUserId: userLogin._id, message: text }, (response) => {
                        if (!response.error) {
                            location.reload()
                        } else {
                            displayError(response.message);
                        }
                    })
                }
            }
        }
    }

    document.querySelector('.send_enter_chat').addEventListener('click', sendChatMessage);

</script>
<!--<script type="text/javascript" src="/template/store/tin-nhan.js?v=<%- cacheVersion %>"></script>-->
