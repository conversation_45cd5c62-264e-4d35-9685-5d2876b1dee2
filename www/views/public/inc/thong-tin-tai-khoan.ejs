<div class="content_boxed">
    <section class="page_title">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="page_title_container">
                        <h1>Thông tin tài khoản</h1>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="sec_portfolio">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="search_portfolio form_schedule edit">
                        <div class="tab_container">
                            <ul class="menu_tab">
                                <li class="active"><PERSON><PERSON> sơ của tôi</li>
                                <li><PERSON><PERSON> hàng</li>
                                <li>Đổi mật khẩu</li>
                            </ul>
                            <div class="content_tab">
                                <div class="inner_box">
                                    <form onsubmit="return false" class="container baseFormDataRequest"
                                          action="/thong-tin-tai-khoan.html" sRedirect='/thong-tin-tai-khoan.html'
                                          method="POST" accept-charset="utf-8">

                                        <% if (userLogin.type == 0){ %>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Họ và tên</label>
                                                        <input type="text" name="fullName"
                                                               value="<%- userLogin.fullName %>">
                                                    </div>
                                                </div>

                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Email</label>
                                                        <input disabled readonly type="email" name="email" value="<%- userLogin.email %>">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">

                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Số điện thoại</label>
                                                        <input type="text" name="phone" value="<%- userLogin.phone %>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Địa chỉ</label>
                                                        <input type="text" name="address"
                                                               value="<%- userLogin.address %>">
                                                    </div>
                                                </div>
                                            </div>
                                        <% }else{ %>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Tên cửa hàng/ pòng khám/ spa</label>
                                                        <input type="text" name="fullName"
                                                               value="<%- userLogin.fullName %>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field ico_time">
                                                        <label for="">Ngày sinh</label>
                                                        <input type="date" name="birthday"
                                                               value="<%- convertDateShow3(userLogin.birthday, 'YYYY-MM-DD') %>">
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Giới tính</label>
                                                        <div class="select_form">
                                                            <select class="" name="sex">
                                                                <option value="all" disabled>Chọn giới tính</option>
                                                                <option value="0" <%- userLogin.sex == 0 ? 'selected' : '' %> >
                                                                    Nam
                                                                </option>
                                                                <option value="1" <%- userLogin.sex == 1 ? 'selected' : '' %>>
                                                                    Nữ
                                                                </option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Số điện thoại</label>
                                                        <input type="text" name="phone" value="<%- userLogin.phone %>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Email</label>
                                                        <input readonly disabled type="email" name="email" value="<%- userLogin.email %>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="input_field schedule_field">
                                                        <label for="">Địa chỉ</label>
                                                        <input type="text" name="address"
                                                               value="<%- userLogin.address %>">
                                                    </div>
                                                </div>

                                            </div>
                                        <% } %>

                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="input_field schedule_field">
                                                    <label for="">Ảnh đại diện</label>
                                                    <div class="upload_container img_child">
                                                        <div class="upfile file_uploaded">
                                                            <label for="upload-product-0">
                                                                <img src="https://storage.googleapis.com/lnbvd-75473.firebasestorage.app/<%- encodeURI(userLogin.picture) %>"
                                                                     style="width:100px; border-radius: 50px; height: 100px;">
                                                            </label>
                                                            <input style="display: none;"
                                                                   class="upload-new-picture upfile-input" type="file"
                                                                   name="picture" id="upload-product-0" accept="image/x-png,image/gif,image/jpeg">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="button_submit">
                                            <a href="/quan-ly-lich-hen.html">Huỷ</a>
                                            <button type="submit" name="update">Lưu</button>
                                        </div>
                                    </form>
                                </div>
                                <div class="inner_box">
                                    <div class="banking_container">
                                        <div class="account_banking">
                                            <div class="title_box">
                                                <h3>Tài Khoản Ngân Hàng Của Tôi</h3>
                                                <a href="/them-tai-khoan-ngan-hang.html" class="add_banking"><img
                                                            src="/template/ui/img/plus.svg" alt=""> Thêm tài
                                                    khoản ngân hàng</a>
                                            </div>
                                            <div class="content_box">
                                                <% accountBanks.forEach(item=>{ %>
                                                    <div class="account_item">
                                                        <div class="logo_banking">
                                                            <img src="/template/ui/img/banking.png" alt="">
                                                        </div>
                                                        <div class="info_banking">
                                                            <h3><%- item.bankName %></h3>
                                                            <ul>
                                                                <li>Họ Và Tên: <%- item.fullName %></li>
                                                                <li>STK: <%- item.bankNumber %></li>
                                                                <li>Chi nhánh: <%- item.bankBrnach %></li>
                                                            </ul>
                                                        </div>

                                                        <div class="delete_banking">
                                                            <a href="javascript:;" accountId='<%- item._id %>'
                                                               class="delete-account"><img
                                                                        src="/template/ui/img/delete-2.svg" alt=""> Xoá</a>
                                                        </div>
                                                    </div>
                                                <% }) %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="inner_box">
                                    <form onsubmit="return false" class="container baseFormRequest"
                                          action="/thong-tin-tai-khoan-doi-mat-khau.html"
                                          sRedirect='/thong-tin-tai-khoan.html' method="POST" accept-charset="utf-8">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="input_field schedule_field">
                                                    <label for="">Mật khẩu hiện tại</label>
                                                    <input type="password" name="password">
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="input_field schedule_field">
                                                    <label for="">Nhập mật khẩu hiện tại</label>
                                                    <input type="password" name="passwordNew">
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="input_field schedule_field">
                                                    <label for="">Xác nhận mật khẩu mới</label>
                                                    <input type="password" name="passwordCFNew">
                                                </div>
                                            </div>
                                            <!-- <div class="col-md-3">
                                                <div class="input_field schedule_field">
                                                    <label for="">Mã xác minh <a href="#" class="send_code">Gửi mã xác
                                                            mình</a></label>
                                                    <input type="text" name="code" placeholder="Nhập mã xác minh">
                                                </div>
                                            </div> -->
                                        </div>
                                        <div class="button_submit">
                                            <button type="submit" name="confirm">Xác nhận</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<script type="text/javascript" src="/template/thong-tin-tai-khoan.js?v=<%- cacheVersion %>"></script>
